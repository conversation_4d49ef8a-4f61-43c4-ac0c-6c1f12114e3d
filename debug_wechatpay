8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Component initialized for postId: 25158 {initialContent: '<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人…\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃...', postTitle: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false, …}graphqlSingleName: "Post"hasInitialPaywallInfo: trueinitialContent: "<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人\r\n \r\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃..."isAuthenticated: falseisUnlocked: falsepaywallVariant: "default"postTitle: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"requiredMemberLevel: 4unlockPrice: 0.02[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Paywall detection for postId: 25158 {hasPaywall: true, isAuthenticated: false, shouldSkipQuery: true}hasPaywall: trueisAuthenticated: falseshouldSkipQuery: true[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Rendering PaywallRenderer for postId: 25158 {timestamp: '2025-08-01T15:29:24.609Z', contentLength: 289, hasPaywallMarker: true, postData: {…}, hasPaywallInfo: true, …}contentLength: 289hasPaywallInfo: truehasPaywallMarker: trueloading: falsepaywallVariant: "default"postData: isUnlocked: falserequiredMemberLevel: 4title: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"unlockPrice: 0.02[[Prototype]]: Objecttimestamp: "2025-08-01T15:29:24.609Z"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Component rendered for postId: 25158 {timestamp: '2025-08-01T15:29:24.611Z', contentLength: 289, hasPaywallMarker: true, unlockPrice: 0.02, requiredMemberLevel: 4, …}contentLength: 289hasPaywallInfo: truehasPaywallMarker: trueisUnlocked: falselegacyMode: falsepaywallInfoHasPaywall: truerequiredMemberLevel: 4showLoading: falsetimestamp: "2025-08-01T15:29:24.611Z"unlockPrice: 0.02userMemberLevel: "none"variant: "default"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Using optimized mode for postId: 25158 {timestamp: '2025-08-01T15:29:24.613Z', paywallInfo: {…}, variant: 'default', isUnlocked: false}isUnlocked: falsepaywallInfo: hasPaywall: trueisLoggedIn: trueloginUrl: "/auth/login"message: "会员专享内容\n此内容为更高级别会员专享。\n升级您的会员"previewContent: "<p>&nbsp;<br>\n&#28909;&#28526;&#27769;&#28044;&#65292;&#20063;&#38459;&#21147;&#37325;&#37325;&#12290;</p><p>&#32534;&#36753;&nbsp;|&nbsp;&#20174;&#26519;<br>\n&#32534;&#35793; | &#26410;&#26469;&#23398;&#20154;<br>\n&#26469;&#28304; | &#32463;&#27982;&#23398;&#20154;<br>\n&nbsp;<br>\n3&#26376;6&#26085;&#65292;&#21495;&#31216;&#20840;&#29699;&#39318;&#27454;&#36890;&#29992;&#22411;Agent&#30340;Manus&#19968;&#32463;&#21457;&#24067;&#65292;&#20854;&#27880;&#20876;&#32593;&#31449;&#23601;&#22240;&#35775;&#38382;&#37327;&#26292;&#22686;&#32780;&#23849;&#28291;&#12290;Manus&#32972;&#21518;&#30340;&#20844;&#21496;&#34676;&#34678;&#25928;&#24212;(Butterf&hellip;</p>\n"registerUrl: "/auth/register"upgradeUrl: "https://www.futuredecade.com/membership/upgrade"__typename: "PaywallInfo"[[Prototype]]: Objecttimestamp: "2025-08-01T15:29:24.613Z"variant: "default"[[Prototype]]: Object
layout-3c8d695f65e7d729.js:1 [WS DEBUG] ========== WebSocketEventHub useEffect START ==========
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Socket available: false
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Current pathname: /article/250413-229279/ai-china-scale
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Route prefixes: {categoryPrefix: null, tagPrefix: 'topics', postPrefix: 'articles', categoryIndexRoute: 'category-index', tagIndexRoute: 'tag-index', …}
layout-3c8d695f65e7d729.js:1 [WS DEBUG] No socket available, returning early
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Content state changed for postId: 25158 {contentLength: 289, hasPaywallMarker: true, contentPreview: '<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人…ss-denied"><h3>会员专享内容</h3><p>此内容为更高级别会员专享。</p>...'}contentLength: 289contentPreview: "<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人\r\n \r\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃。Manus背后的公司蝴蝶效应(Butterf…</p><div class=\"fd-member-access-denied\"><h3>会员专享内容</h3><p>此内容为更高级别会员专享。</p>..."hasPaywallMarker: true[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] PostData state changed for postId: 25158 {title: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false}isUnlocked: falserequiredMemberLevel: 4title: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"unlockPrice: 0.02[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] PaywallInfo state changed for postId: 25158 {hasPaywall: true, previewContent: '<p>&nbsp;<br>\n&#28909;&#28526;&#27769;&#28044;&#65…676;&#34678;&#25928;&#24212;(Butterf&hellip;</p>\n', loginUrl: '/auth/login', registerUrl: '/auth/register', upgradeUrl: 'https://www.futuredecade.com/membership/upgrade', …}hasPaywall: trueisLoggedIn: trueloginUrl: "/auth/login"message: "会员专享内容\n此内容为更高级别会员专享。\n升级您的会员"previewContent: "<p>&nbsp;<br>\n&#28909;&#28526;&#27769;&#28044;&#65292;&#20063;&#38459;&#21147;&#37325;&#37325;&#12290;</p><p>&#32534;&#36753;&nbsp;|&nbsp;&#20174;&#26519;<br>\n&#32534;&#35793; | &#26410;&#26469;&#23398;&#20154;<br>\n&#26469;&#28304; | &#32463;&#27982;&#23398;&#20154;<br>\n&nbsp;<br>\n3&#26376;6&#26085;&#65292;&#21495;&#31216;&#20840;&#29699;&#39318;&#27454;&#36890;&#29992;&#22411;Agent&#30340;Manus&#19968;&#32463;&#21457;&#24067;&#65292;&#20854;&#27880;&#20876;&#32593;&#31449;&#23601;&#22240;&#35775;&#38382;&#37327;&#26292;&#22686;&#32780;&#23849;&#28291;&#12290;Manus&#32972;&#21518;&#30340;&#20844;&#21496;&#34676;&#34678;&#25928;&#24212;(Butterf&hellip;</p>\n"registerUrl: "/auth/register"upgradeUrl: "https://www.futuredecade.com/membership/upgrade"__typename: "PaywallInfo"[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Props changed, syncing to state for postId: 25158 {oldContent: '<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人…\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃...', newContent: '<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人…\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃...', oldPostData: {…}, newPostData: {…}}newContent: "<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人\r\n \r\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃..."newPostData: {title: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false}oldContent: "<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人\r\n \r\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃..."oldPostData: {title: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false}[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] GraphQL query state changed for postId: 25158 {loading: false, hasData: false, dataKeys: Array(0), skip: true}dataKeys: Array(0)length: 0[[Prototype]]: Array(0)hasData: falseloading: falseskip: true[[Prototype]]: Object
7269-0fa07f0679a526ba.js:1 初始化: 找到认证令牌
7269-0fa07f0679a526ba.js:1 初始化: 使用缓存的用户数据
7269-0fa07f0679a526ba.js:1 初始化: 后台验证用户数据
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Component initialized for postId: 25158 {initialContent: '<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人…\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃...', postTitle: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false, …}graphqlSingleName: "Post"hasInitialPaywallInfo: trueinitialContent: "<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人\r\n \r\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃..."isAuthenticated: trueisUnlocked: falsepaywallVariant: "default"postTitle: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"requiredMemberLevel: 4unlockPrice: 0.02[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Paywall detection for postId: 25158 {hasPaywall: true, isAuthenticated: true, shouldSkipQuery: false}hasPaywall: trueisAuthenticated: trueshouldSkipQuery: false[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Rendering loading state for postId: 25158 {timestamp: '2025-08-01T15:29:24.807Z', loading: true, hasData: false}hasData: falseloading: truetimestamp: "2025-08-01T15:29:24.807Z"[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Component initialized for postId: 25158 {initialContent: '<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人…\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃...', postTitle: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false, …}graphqlSingleName: "Post"hasInitialPaywallInfo: trueinitialContent: "<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人\r\n \r\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃..."isAuthenticated: trueisUnlocked: falsepaywallVariant: "default"postTitle: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"requiredMemberLevel: 4unlockPrice: 0.02[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Paywall detection for postId: 25158 {hasPaywall: true, isAuthenticated: true, shouldSkipQuery: false}hasPaywall: trueisAuthenticated: trueshouldSkipQuery: false[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Rendering loading state for postId: 25158 {timestamp: '2025-08-01T15:29:24.872Z', loading: true, hasData: false}hasData: falseloading: truetimestamp: "2025-08-01T15:29:24.872Z"[[Prototype]]: Object
layout-3c8d695f65e7d729.js:1 [WS DEBUG] ========== WebSocketEventHub useEffect START ==========
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Socket available: false
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Current pathname: /article/250413-229279/ai-china-scale
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Route prefixes: {categoryPrefix: null, tagPrefix: 'topics', postPrefix: 'articles', categoryIndexRoute: 'category-index', tagIndexRoute: 'tag-index', …}
layout-3c8d695f65e7d729.js:1 [WS DEBUG] No socket available, returning early
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] PostData state changed for postId: 25158 {title: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false}
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] GraphQL query state changed for postId: 25158 {loading: true, hasData: false, dataKeys: Array(0), skip: false}
layout-3c8d695f65e7d729.js:1 [WS DEBUG] ========== WebSocketEventHub useEffect START ==========
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Socket available: true
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Current pathname: /article/250413-229279/ai-china-scale
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Route prefixes: {categoryPrefix: null, tagPrefix: 'topics', postPrefix: 'articles', categoryIndexRoute: 'category-index', tagIndexRoute: 'tag-index', …}
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Socket connection state: true
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Socket ID: cgUF9F8Kkc95Gl-sAAAc
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Registering event listeners...
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Event listeners registered: (17) ['post:updated', 'post:updated-for-lists', 'post:inserted', 'post:deleted', 'post:status-published', 'post:status-unpublished', 'post:unlocked', 'tag:updated', 'category:updated', 'taxonomy:updated', 'list:item-added', 'list:item-removed', 'menu:updated', 'menu:created', 'menu:deleted', 'menu:locations-updated', 'menu:options-updated']
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Cleaning up event listeners...
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Event listeners cleaned up
layout-3c8d695f65e7d729.js:1 [WS DEBUG] ========== WebSocketEventHub useEffect END ==========
layout-3c8d695f65e7d729.js:1 [WS DEBUG] ========== WebSocketEventHub useEffect START ==========
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Socket available: true
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Current pathname: /article/250413-229279/ai-china-scale
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Route prefixes: {__typename: 'RouteSettings', categoryPrefix: null, tagPrefix: null, postPrefix: 'article', categoryIndexRoute: 'intelligence', …}
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Socket connection state: true
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Socket ID: cgUF9F8Kkc95Gl-sAAAc
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Registering event listeners...
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Event listeners registered: (17) ['post:updated', 'post:updated-for-lists', 'post:inserted', 'post:deleted', 'post:status-published', 'post:status-unpublished', 'post:unlocked', 'tag:updated', 'category:updated', 'taxonomy:updated', 'list:item-added', 'list:item-removed', 'menu:updated', 'menu:created', 'menu:deleted', 'menu:locations-updated', 'menu:options-updated']
7269-0fa07f0679a526ba.js:1 初始化: 验证成功，更新用户数据
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Component initialized for postId: 25158 {initialContent: '<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人…\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃...', postTitle: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false, …}graphqlSingleName: "Post"hasInitialPaywallInfo: trueinitialContent: "<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人\r\n \r\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃..."isAuthenticated: trueisUnlocked: falsepaywallVariant: "default"postTitle: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"requiredMemberLevel: 4unlockPrice: 0.02[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Paywall detection for postId: 25158 {hasPaywall: true, isAuthenticated: true, shouldSkipQuery: false}hasPaywall: trueisAuthenticated: trueshouldSkipQuery: false[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Rendering loading state for postId: 25158 {timestamp: '2025-08-01T15:29:26.003Z', loading: true, hasData: false}hasData: falseloading: truetimestamp: "2025-08-01T15:29:26.003Z"[[Prototype]]: Object
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Cleaning up event listeners...
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Event listeners cleaned up
layout-3c8d695f65e7d729.js:1 [WS DEBUG] ========== WebSocketEventHub useEffect END ==========
layout-3c8d695f65e7d729.js:1 [WS DEBUG] ========== WebSocketEventHub useEffect START ==========
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Socket available: true
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Current pathname: /article/250413-229279/ai-china-scale
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Route prefixes: {__typename: 'RouteSettings', categoryPrefix: null, tagPrefix: null, postPrefix: 'article', categoryIndexRoute: 'intelligence', …}
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Socket connection state: true
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Socket ID: skcHe4RhfQ5my0FwAAAe
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Registering event listeners...
layout-3c8d695f65e7d729.js:1 [WS DEBUG] Event listeners registered: (17) ['post:updated', 'post:updated-for-lists', 'post:inserted', 'post:deleted', 'post:status-published', 'post:status-unpublished', 'post:unlocked', 'tag:updated', 'category:updated', 'taxonomy:updated', 'list:item-added', 'list:item-removed', 'menu:updated', 'menu:created', 'menu:deleted', 'menu:locations-updated', 'menu:options-updated']
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Component initialized for postId: 25158 {initialContent: '<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人…\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃...', postTitle: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false, …}graphqlSingleName: "Post"hasInitialPaywallInfo: trueinitialContent: "<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人\r\n \r\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃..."isAuthenticated: trueisUnlocked: falsepaywallVariant: "default"postTitle: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"requiredMemberLevel: 4unlockPrice: 0.02[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Paywall detection for postId: 25158 {hasPaywall: true, isAuthenticated: true, shouldSkipQuery: false}hasPaywall: trueisAuthenticated: trueshouldSkipQuery: false[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Rendering PaywallRenderer for postId: 25158 {timestamp: '2025-08-01T15:29:26.500Z', contentLength: 289, hasPaywallMarker: true, postData: {…}, hasPaywallInfo: true, …}contentLength: 289hasPaywallInfo: truehasPaywallMarker: trueloading: falsepaywallVariant: "default"postData: isUnlocked: falserequiredMemberLevel: 4title: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"unlockPrice: 0.02[[Prototype]]: Objecttimestamp: "2025-08-01T15:29:26.500Z"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Component rendered for postId: 25158 {timestamp: '2025-08-01T15:29:26.501Z', contentLength: 289, hasPaywallMarker: true, unlockPrice: 0.02, requiredMemberLevel: 4, …}contentLength: 289hasPaywallInfo: truehasPaywallMarker: trueisUnlocked: falselegacyMode: falsepaywallInfoHasPaywall: truerequiredMemberLevel: 4showLoading: falsetimestamp: "2025-08-01T15:29:26.501Z"unlockPrice: 0.02userMemberLevel: 3variant: "default"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Using optimized mode for postId: 25158 {timestamp: '2025-08-01T15:29:26.502Z', paywallInfo: {…}, variant: 'default', isUnlocked: false}isUnlocked: falsepaywallInfo: hasPaywall: trueisLoggedIn: trueloginUrl: "/auth/login"message: "会员专享内容\n此内容为更高级别会员专享。\n升级您的会员"previewContent: "<p>&nbsp;<br>\n&#28909;&#28526;&#27769;&#28044;&#65292;&#20063;&#38459;&#21147;&#37325;&#37325;&#12290;</p><p>&#32534;&#36753;&nbsp;|&nbsp;&#20174;&#26519;<br>\n&#32534;&#35793; | &#26410;&#26469;&#23398;&#20154;<br>\n&#26469;&#28304; | &#32463;&#27982;&#23398;&#20154;<br>\n&nbsp;<br>\n3&#26376;6&#26085;&#65292;&#21495;&#31216;&#20840;&#29699;&#39318;&#27454;&#36890;&#29992;&#22411;Agent&#30340;Manus&#19968;&#32463;&#21457;&#24067;&#65292;&#20854;&#27880;&#20876;&#32593;&#31449;&#23601;&#22240;&#35775;&#38382;&#37327;&#26292;&#22686;&#32780;&#23849;&#28291;&#12290;Manus&#32972;&#21518;&#30340;&#20844;&#21496;&#34676;&#34678;&#25928;&#24212;(Butterf&hellip;</p>\n"registerUrl: "/auth/register"upgradeUrl: "https://www.futuredecade.com/membership/upgrade"__typename: "PaywallInfo"[[Prototype]]: Objecttimestamp: "2025-08-01T15:29:26.502Z"variant: "default"[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] GraphQL query state changed for postId: 25158 {loading: false, hasData: true, dataKeys: Array(1), skip: false}dataKeys: Array(1)0: "contentNode"length: 1[[Prototype]]: Array(0)hasData: trueloading: falseskip: false[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Received new GraphQL data for postId: 25158 {timestamp: '2025-08-01T15:29:26.512Z', nodeKeys: Array(5), hasContent: false, contentLength: 0, contentPreview: 'undefined...', …}contentLength: 0contentPreview: "undefined..."hasContent: falsehasPaywallInfo: truehasPaywallMarker: falseisUnlockedByCurrentUser: falsenodeKeys: Array(5)0: "__typename"1: "unlockPrice"2: "requiredMemberLevel"3: "isUnlockedByCurrentUser"4: "paywallInfo"length: 5[[Prototype]]: Array(0)paywallInfoDetails: hasPaywall: trueisLoggedIn: trueloginUrl: "/auth/login"message: "会员专享内容\n此内容为更高级别会员专享。\n升级您的会员"previewContent: "<p>&nbsp;<br>\n&#28909;&#28526;&#27769;&#28044;&#65292;&#20063;&#38459;&#21147;&#37325;&#37325;&#12290;</p><p>&#32534;&#36753;&nbsp;|&nbsp;&#20174;&#26519;<br>\n&#32534;&#35793; | &#26410;&#26469;&#23398;&#20154;<br>\n&#26469;&#28304; | &#32463;&#27982;&#23398;&#20154;<br>\n&nbsp;<br>\n3&#26376;6&#26085;&#65292;&#21495;&#31216;&#20840;&#29699;&#39318;&#27454;&#36890;&#29992;&#22411;Agent&#30340;Manus&#19968;&#32463;&#21457;&#24067;&#65292;&#20854;&#27880;&#20876;&#32593;&#31449;&#23601;&#22240;&#35775;&#38382;&#37327;&#26292;&#22686;&#32780;&#23849;&#28291;&#12290;Manus&#32972;&#21518;&#30340;&#20844;&#21496;&#34676;&#34678;&#25928;&#24212;(Butterf&hellip;</p>\n"registerUrl: "/auth/register"upgradeUrl: "https://www.futuredecade.com/membership/upgrade"__typename: "PaywallInfo"[[Prototype]]: ObjectrequiredMemberLevel: 4timestamp: "2025-08-01T15:29:26.512Z"unlockPrice: 0.02[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] State updated after GraphQL data for postId: 25158 {contentChanged: false, postDataChanged: false, paywallInfoChanged: true}contentChanged: falsepaywallInfoChanged: truepostDataChanged: false[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Component initialized for postId: 25158 {initialContent: '<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人…\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃...', postTitle: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false, …}graphqlSingleName: "Post"hasInitialPaywallInfo: trueinitialContent: "<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人\r\n \r\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃..."isAuthenticated: trueisUnlocked: falsepaywallVariant: "default"postTitle: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"requiredMemberLevel: 4unlockPrice: 0.02[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Paywall detection for postId: 25158 {hasPaywall: true, isAuthenticated: true, shouldSkipQuery: false}hasPaywall: trueisAuthenticated: trueshouldSkipQuery: false[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Rendering PaywallRenderer for postId: 25158 {timestamp: '2025-08-01T15:29:26.517Z', contentLength: 289, hasPaywallMarker: true, postData: {…}, hasPaywallInfo: true, …}contentLength: 289hasPaywallInfo: truehasPaywallMarker: trueloading: falsepaywallVariant: "default"postData: {title: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false}timestamp: "2025-08-01T15:29:26.517Z"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Component rendered for postId: 25158 {timestamp: '2025-08-01T15:29:26.517Z', contentLength: 289, hasPaywallMarker: true, unlockPrice: 0.02, requiredMemberLevel: 4, …}contentLength: 289hasPaywallInfo: truehasPaywallMarker: trueisUnlocked: falselegacyMode: falsepaywallInfoHasPaywall: truerequiredMemberLevel: 4showLoading: falsetimestamp: "2025-08-01T15:29:26.517Z"unlockPrice: 0.02userMemberLevel: 3variant: "default"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Using optimized mode for postId: 25158 {timestamp: '2025-08-01T15:29:26.518Z', paywallInfo: {…}, variant: 'default', isUnlocked: false}isUnlocked: falsepaywallInfo: hasPaywall: trueisLoggedIn: trueloginUrl: "/auth/login"message: "会员专享内容\n此内容为更高级别会员专享。\n升级您的会员"previewContent: "<p>&nbsp;<br>\n&#28909;&#28526;&#27769;&#28044;&#65292;&#20063;&#38459;&#21147;&#37325;&#37325;&#12290;</p><p>&#32534;&#36753;&nbsp;|&nbsp;&#20174;&#26519;<br>\n&#32534;&#35793; | &#26410;&#26469;&#23398;&#20154;<br>\n&#26469;&#28304; | &#32463;&#27982;&#23398;&#20154;<br>\n&nbsp;<br>\n3&#26376;6&#26085;&#65292;&#21495;&#31216;&#20840;&#29699;&#39318;&#27454;&#36890;&#29992;&#22411;Agent&#30340;Manus&#19968;&#32463;&#21457;&#24067;&#65292;&#20854;&#27880;&#20876;&#32593;&#31449;&#23601;&#22240;&#35775;&#38382;&#37327;&#26292;&#22686;&#32780;&#23849;&#28291;&#12290;Manus&#32972;&#21518;&#30340;&#20844;&#21496;&#34676;&#34678;&#25928;&#24212;(Butterf&hellip;</p>\n"registerUrl: "/auth/register"upgradeUrl: "https://www.futuredecade.com/membership/upgrade"__typename: "PaywallInfo"[[Prototype]]: Objecttimestamp: "2025-08-01T15:29:26.518Z"variant: "default"[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] PostData state changed for postId: 25158 {title: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false}isUnlocked: falserequiredMemberLevel: 4title: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"unlockPrice: 0.02[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] PaywallInfo state changed for postId: 25158 {__typename: 'PaywallInfo', hasPaywall: true, previewContent: '<p>&nbsp;<br>\n&#28909;&#28526;&#27769;&#28044;&#65…676;&#34678;&#25928;&#24212;(Butterf&hellip;</p>\n', loginUrl: '/auth/login', registerUrl: '/auth/register', …}hasPaywall: trueisLoggedIn: trueloginUrl: "/auth/login"message: "会员专享内容\n此内容为更高级别会员专享。\n升级您的会员"previewContent: "<p>&nbsp;<br>\n&#28909;&#28526;&#27769;&#28044;&#65292;&#20063;&#38459;&#21147;&#37325;&#37325;&#12290;</p><p>&#32534;&#36753;&nbsp;|&nbsp;&#20174;&#26519;<br>\n&#32534;&#35793; | &#26410;&#26469;&#23398;&#20154;<br>\n&#26469;&#28304; | &#32463;&#27982;&#23398;&#20154;<br>\n&nbsp;<br>\n3&#26376;6&#26085;&#65292;&#21495;&#31216;&#20840;&#29699;&#39318;&#27454;&#36890;&#29992;&#22411;Agent&#30340;Manus&#19968;&#32463;&#21457;&#24067;&#65292;&#20854;&#27880;&#20876;&#32593;&#31449;&#23601;&#22240;&#35775;&#38382;&#37327;&#26292;&#22686;&#32780;&#23849;&#28291;&#12290;Manus&#32972;&#21518;&#30340;&#20844;&#21496;&#34676;&#34678;&#25928;&#24212;(Butterf&hellip;</p>\n"registerUrl: "/auth/register"upgradeUrl: "https://www.futuredecade.com/membership/upgrade"__typename: "PaywallInfo"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Component rendered for postId: 25158 {timestamp: '2025-08-01T15:29:26.807Z', contentLength: 289, hasPaywallMarker: true, unlockPrice: 0.02, requiredMemberLevel: 4, …}contentLength: 289hasPaywallInfo: truehasPaywallMarker: trueisUnlocked: falselegacyMode: falsepaywallInfoHasPaywall: truerequiredMemberLevel: 4showLoading: falsetimestamp: "2025-08-01T15:29:26.807Z"unlockPrice: 0.02userMemberLevel: 3variant: "default"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Using optimized mode for postId: 25158 {timestamp: '2025-08-01T15:29:26.808Z', paywallInfo: {…}, variant: 'default', isUnlocked: false}isUnlocked: falsepaywallInfo: hasPaywall: trueisLoggedIn: trueloginUrl: "/auth/login"message: "会员专享内容\n此内容为更高级别会员专享。\n升级您的会员"previewContent: "<p>&nbsp;<br>\n&#28909;&#28526;&#27769;&#28044;&#65292;&#20063;&#38459;&#21147;&#37325;&#37325;&#12290;</p><p>&#32534;&#36753;&nbsp;|&nbsp;&#20174;&#26519;<br>\n&#32534;&#35793; | &#26410;&#26469;&#23398;&#20154;<br>\n&#26469;&#28304; | &#32463;&#27982;&#23398;&#20154;<br>\n&nbsp;<br>\n3&#26376;6&#26085;&#65292;&#21495;&#31216;&#20840;&#29699;&#39318;&#27454;&#36890;&#29992;&#22411;Agent&#30340;Manus&#19968;&#32463;&#21457;&#24067;&#65292;&#20854;&#27880;&#20876;&#32593;&#31449;&#23601;&#22240;&#35775;&#38382;&#37327;&#26292;&#22686;&#32780;&#23849;&#28291;&#12290;Manus&#32972;&#21518;&#30340;&#20844;&#21496;&#34676;&#34678;&#25928;&#24212;(Butterf&hellip;</p>\n"registerUrl: "/auth/register"upgradeUrl: "https://www.futuredecade.com/membership/upgrade"__typename: "PaywallInfo"[[Prototype]]: Objecttimestamp: "2025-08-01T15:29:26.808Z"variant: "default"[[Prototype]]: Object
4938-5dc0d63335f41a1f.js:1  GET https://www.futuredecade.com/membership/upgrade?returnUrl=https%3A%2F%2Fwww.futuredecade.com%2Farticle%2F250413-229279%2Fai-china-scale&_rsc=1k3n4 500 (Internal Server Error)
s @ 4938-5dc0d63335f41a1f.js:1
(anonymous) @ 4938-5dc0d63335f41a1f.js:1
u @ 4938-5dc0d63335f41a1f.js:1
s @ 4938-5dc0d63335f41a1f.js:1
enqueue @ 4938-5dc0d63335f41a1f.js:1
c @ 4938-5dc0d63335f41a1f.js:1
f @ 4938-5dc0d63335f41a1f.js:1
action @ 4938-5dc0d63335f41a1f.js:1
c @ 4938-5dc0d63335f41a1f.js:1
(anonymous) @ 4938-5dc0d63335f41a1f.js:1
dispatch @ 4938-5dc0d63335f41a1f.js:1
(anonymous) @ 4938-5dc0d63335f41a1f.js:1
(anonymous) @ 4938-5dc0d63335f41a1f.js:1
t.startTransition @ 4938-5dc0d63335f41a1f.js:1
prefetch @ 4938-5dc0d63335f41a1f.js:1
g @ 1396-0fd72ba93f9364d7.js:1
(anonymous) @ 1396-0fd72ba93f9364d7.js:1
aD @ fd9d1056-757668b9933ab0bf.js:1
a1 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
a4 @ fd9d1056-757668b9933ab0bf.js:1
a6 @ fd9d1056-757668b9933ab0bf.js:1
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallCard] Unlock button clicked for postId: 25158 {timestamp: '2025-08-01T15:29:30.429Z', currentStep: 'initial', isAuthenticated: true, unlockPrice: 0.02, isUnlocked: false}currentStep: "initial"isAuthenticated: trueisUnlocked: falsetimestamp: "2025-08-01T15:29:30.429Z"unlockPrice: 0.02[[Prototype]]: Object
image:1  GET https://www.futuredecade.com/_next/image?url=%3Csvg%20class%3D%22icon%22%20viewBox%3D%220%200%201024%201024%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22128%22%20height%3D%22128%22%3E%3Cpath%20d%3D%22M436.636%20385.155c107.161%200%20218.624-23.568%20285.558-68.505v81.809h62.01V219.33c0-107.678-179.02-165.825-347.46-165.825-168.449%200-347.577%2058.147-347.577%20165.832v488.886c0%20107.685%20179.028%20165.825%20347.469%20165.825v-61.903c-176.928%200-285.566-60.548-285.566-103.915V561.258c66.949%2044.836%20178.297%2068.512%20285.566%2068.512v-62.024c-176.928%200-285.566-60.541-285.566-103.908V316.765c66.949%2044.822%20178.411%2068.39%20285.566%2068.39zm0-269.732c176.935%200%20285.558%2060.548%20285.558%20103.915%200%2043.366-108.63%20103.914-285.558%20103.914S151.07%20262.704%20151.07%20219.338c.014-43.367%20108.738-103.922%20285.566-103.922zm270.642%20310.84c-153.883%200-234.336%2059.194-234.336%20117.642v314.159c0%2058.448%2080.554%20117.634%20234.336%20117.634%20152.736%200%20233.075-58.348%20234.322-116.38h.114V543.912c-.107-58.455-80.56-117.648-234.436-117.648zm0%2061.91c113.756%200%20172.426%2039.066%20172.426%2055.732%200%2016.658-58.663%2055.724-172.426%2055.724s-172.426-39.066-172.426-55.724%2058.662-55.731%20172.426-55.731zm0%20425.615c-113.764%200-172.426-39.066-172.426-55.724v-75.006c39.28%2021.375%2097.004%2035.625%20172.426%2035.625%2075.414%200%20133.038-14.243%20172.426-35.625v75.006c0%2016.658-58.663%2055.724-172.426%2055.724zm0-157.123c-113.764%200-172.426-39.087-172.426-55.73v-75.007c39.28%2021.375%2097.004%2035.61%20172.426%2035.61%2075.414%200%20133.038-14.235%20172.426-35.61v75.006c0%2016.644-58.663%2055.724-172.426%2055.724z%22%2F%3E%3C%2Fsvg%3E&w=96&q=75 400 (Bad Request)
image:1  GET https://www.futuredecade.com/_next/image?url=%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2022%2018%22%3E%3Cpath%20d%3D%22M20.005%203h-7a6%206%200%201%200%200%2012h7v2a1%201%200%200%201-1%201h-18a1%201%200%200%201-1-1V1a1%201%200%200%201%201-1h18a1%201%200%200%201%201%201v2zm-7%202h8v8h-8a4%204%200%201%201%200-8zm0%203v2h3V8h-3z%22%2F%3E%3C%2Fsvg%3E&w=96&q=75 400 (Bad Request)
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallCard] Creating unlock order for postId: 25158 {timestamp: '2025-08-01T15:29:33.889Z', selectedMethod: 'wxpay', callbackUrl: 'https://www.futuredecade.com/article/250413-229279/ai-china-scale'}callbackUrl: "https://www.futuredecade.com/article/250413-229279/ai-china-scale"selectedMethod: "wxpay"timestamp: "2025-08-01T15:29:33.889Z"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallCard] CreateUnlockOrder response for postId: 25158 {timestamp: '2025-08-01T15:29:34.141Z', success: true, orderId: '181', message: 'Order created successfully.'}message: "Order created successfully."orderId: "181"success: truetimestamp: "2025-08-01T15:29:34.141Z"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallCard] Order created successfully, moving to payment processing for postId: 25158, orderId: 181
628-0d44fe338eeafc6b.js:1 [DEBUG-PaymentProcessor] Payment status check - not paid yet for orderId: 181 {timestamp: '2025-08-01T15:29:43.036Z', paymentStatus: 'UNPAID', isPaid: false}isPaid: falsepaymentStatus: "UNPAID"timestamp: "2025-08-01T15:29:43.036Z"[[Prototype]]: Object
628-0d44fe338eeafc6b.js:1 [DEBUG-PaymentProcessor] Payment confirmed as paid for orderId: 181 {timestamp: '2025-08-01T15:29:51.387Z', checkPaymentResponse: {…}, productType: undefined, productId: undefined}checkPaymentResponse: isPaid: truepaymentStatus: "PAID"status: true__typename: "CheckPaymentStatusPayload"[[Prototype]]: ObjectproductId: undefinedproductType: undefinedtimestamp: "2025-08-01T15:29:51.387Z"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallCard] Payment success for postId: 25158 {timestamp: '2025-08-01T15:29:51.387Z', currentStep: 'processing_payment', orderId: '181', selectedMethod: 'wxpay', isUnlocked: false}currentStep: "processing_payment"isUnlocked: falseorderId: "181"selectedMethod: "wxpay"timestamp: "2025-08-01T15:29:51.387Z"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallCard] Calling onUnlock callback for postId: 25158...
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Unlock success callback triggered for postId: 25158 {timestamp: '2025-08-01T15:29:51.387Z', currentLoading: false, currentContent: '<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人…\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃...', currentPostData: {…}, currentPaywallInfo: {…}}currentContent: "<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人\r\n \r\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃..."currentLoading: falsecurrentPaywallInfo: hasPaywall: trueisLoggedIn: trueloginUrl: "/auth/login"message: "会员专享内容\n此内容为更高级别会员专享。\n升级您的会员"previewContent: "<p>&nbsp;<br>\n&#28909;&#28526;&#27769;&#28044;&#65292;&#20063;&#38459;&#21147;&#37325;&#37325;&#12290;</p><p>&#32534;&#36753;&nbsp;|&nbsp;&#20174;&#26519;<br>\n&#32534;&#35793; | &#26410;&#26469;&#23398;&#20154;<br>\n&#26469;&#28304; | &#32463;&#27982;&#23398;&#20154;<br>\n&nbsp;<br>\n3&#26376;6&#26085;&#65292;&#21495;&#31216;&#20840;&#29699;&#39318;&#27454;&#36890;&#29992;&#22411;Agent&#30340;Manus&#19968;&#32463;&#21457;&#24067;&#65292;&#20854;&#27880;&#20876;&#32593;&#31449;&#23601;&#22240;&#35775;&#38382;&#37327;&#26292;&#22686;&#32780;&#23849;&#28291;&#12290;Manus&#32972;&#21518;&#30340;&#20844;&#21496;&#34676;&#34678;&#25928;&#24212;(Butterf&hellip;</p>\n"registerUrl: "/auth/register"upgradeUrl: "https://www.futuredecade.com/membership/upgrade"__typename: "PaywallInfo"[[Prototype]]: Objectconstructor: ƒ Object()hasOwnProperty: ƒ hasOwnProperty()isPrototypeOf: ƒ isPrototypeOf()propertyIsEnumerable: ƒ propertyIsEnumerable()toLocaleString: ƒ toLocaleString()toString: ƒ toString()valueOf: ƒ valueOf()__defineGetter__: ƒ __defineGetter__()__defineSetter__: ƒ __defineSetter__()__lookupGetter__: ƒ __lookupGetter__()__lookupSetter__: ƒ __lookupSetter__()__proto__: (...)get __proto__: ƒ __proto__()set __proto__: ƒ __proto__()currentPostData: isUnlocked: falserequiredMemberLevel: 4title: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"unlockPrice: 0.02[[Prototype]]: Objecttimestamp: "2025-08-01T15:29:51.387Z"[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Calling refetch() for postId: 25158...
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Component initialized for postId: 25158 {initialContent: '<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人…\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃...', postTitle: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false, …}graphqlSingleName: "Post"hasInitialPaywallInfo: trueinitialContent: "<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人\r\n \r\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃..."isAuthenticated: trueisUnlocked: falsepaywallVariant: "default"postTitle: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"requiredMemberLevel: 4unlockPrice: 0.02[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Paywall detection for postId: 25158 {hasPaywall: true, isAuthenticated: true, shouldSkipQuery: false}hasPaywall: trueisAuthenticated: trueshouldSkipQuery: false[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Rendering loading state for postId: 25158 {timestamp: '2025-08-01T15:29:51.390Z', loading: true, hasData: true}hasData: trueloading: truetimestamp: "2025-08-01T15:29:51.390Z"[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] GraphQL query state changed for postId: 25158 {loading: true, hasData: true, dataKeys: Array(1), skip: false}dataKeys: ['contentNode']hasData: trueloading: trueskip: false[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Component initialized for postId: 25158 {initialContent: '<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人…\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃...', postTitle: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false, …}graphqlSingleName: "Post"hasInitialPaywallInfo: trueinitialContent: "<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人\r\n \r\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃..."isAuthenticated: trueisUnlocked: falsepaywallVariant: "default"postTitle: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"requiredMemberLevel: 4unlockPrice: 0.02[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Paywall detection for postId: 25158 {hasPaywall: true, isAuthenticated: true, shouldSkipQuery: false}hasPaywall: trueisAuthenticated: trueshouldSkipQuery: false[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Rendering PaywallRenderer for postId: 25158 {timestamp: '2025-08-01T15:29:52.225Z', contentLength: 289, hasPaywallMarker: true, postData: {…}, hasPaywallInfo: true, …}contentLength: 289hasPaywallInfo: truehasPaywallMarker: trueloading: falsepaywallVariant: "default"postData: {title: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false}timestamp: "2025-08-01T15:29:52.225Z"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Component rendered for postId: 25158 {timestamp: '2025-08-01T15:29:52.225Z', contentLength: 289, hasPaywallMarker: true, unlockPrice: 0.02, requiredMemberLevel: 4, …}contentLength: 289hasPaywallInfo: truehasPaywallMarker: trueisUnlocked: falselegacyMode: falsepaywallInfoHasPaywall: truerequiredMemberLevel: 4showLoading: falsetimestamp: "2025-08-01T15:29:52.225Z"unlockPrice: 0.02userMemberLevel: 3variant: "default"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Using optimized mode for postId: 25158 {timestamp: '2025-08-01T15:29:52.225Z', paywallInfo: {…}, variant: 'default', isUnlocked: false}isUnlocked: falsepaywallInfo: hasPaywall: trueisLoggedIn: trueloginUrl: "/auth/login"message: "会员专享内容\n此内容为更高级别会员专享。\n升级您的会员"previewContent: "<p>&nbsp;<br>\n&#28909;&#28526;&#27769;&#28044;&#65292;&#20063;&#38459;&#21147;&#37325;&#37325;&#12290;</p><p>&#32534;&#36753;&nbsp;|&nbsp;&#20174;&#26519;<br>\n&#32534;&#35793; | &#26410;&#26469;&#23398;&#20154;<br>\n&#26469;&#28304; | &#32463;&#27982;&#23398;&#20154;<br>\n&nbsp;<br>\n3&#26376;6&#26085;&#65292;&#21495;&#31216;&#20840;&#29699;&#39318;&#27454;&#36890;&#29992;&#22411;Agent&#30340;Manus&#19968;&#32463;&#21457;&#24067;&#65292;&#20854;&#27880;&#20876;&#32593;&#31449;&#23601;&#22240;&#35775;&#38382;&#37327;&#26292;&#22686;&#32780;&#23849;&#28291;&#12290;Manus&#32972;&#21518;&#30340;&#20844;&#21496;&#34676;&#34678;&#25928;&#24212;(Butterf&hellip;</p>\n"registerUrl: "/auth/register"upgradeUrl: "https://www.futuredecade.com/membership/upgrade"__typename: "PaywallInfo"[[Prototype]]: Objecttimestamp: "2025-08-01T15:29:52.225Z"variant: "default"[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] GraphQL query state changed for postId: 25158 {loading: false, hasData: true, dataKeys: Array(1), skip: false}dataKeys: Array(1)0: "contentNode"length: 1[[Prototype]]: Array(0)hasData: trueloading: falseskip: false[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Received new GraphQL data for postId: 25158 {timestamp: '2025-08-01T15:29:52.237Z', nodeKeys: Array(5), hasContent: false, contentLength: 0, contentPreview: 'undefined...', …}contentLength: 0contentPreview: "undefined..."hasContent: falsehasPaywallInfo: falsehasPaywallMarker: falseisUnlockedByCurrentUser: truenodeKeys: (5) ['__typename', 'unlockPrice', 'requiredMemberLevel', 'isUnlockedByCurrentUser', 'paywallInfo']paywallInfoDetails: nullrequiredMemberLevel: 4timestamp: "2025-08-01T15:29:52.237Z"unlockPrice: 0.02[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] State updated after GraphQL data for postId: 25158 {contentChanged: false, postDataChanged: true, paywallInfoChanged: true}contentChanged: falsepaywallInfoChanged: truepostDataChanged: true[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Refetch completed for postId: 25158 {timestamp: '2025-08-01T15:29:52.243Z', hasData: true, dataKeys: Array(1), loading: false, error: undefined}dataKeys: Array(1)0: "contentNode"length: 1[[Prototype]]: Array(0)error: undefinedhasData: trueloading: falsetimestamp: "2025-08-01T15:29:52.243Z"[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Component initialized for postId: 25158 {initialContent: '<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人…\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃...', postTitle: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: false, …}graphqlSingleName: "Post"hasInitialPaywallInfo: trueinitialContent: "<p> \r\n热潮汹涌，也阻力重重。\r\n\r\n编辑 | 从林\r\n编译 | 未来学人\r\n来源 | 经济学人\r\n \r\n3月6日，号称全球首款通用型Agent的Manus一经发布，其注册网站就因访问量暴增而崩溃..."isAuthenticated: trueisUnlocked: falsepaywallVariant: "default"postTitle: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"requiredMemberLevel: 4unlockPrice: 0.02[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Paywall detection for postId: 25158 {hasPaywall: true, isAuthenticated: true, shouldSkipQuery: false}hasPaywall: trueisAuthenticated: trueshouldSkipQuery: false[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Rendering PaywallRenderer for postId: 25158 {timestamp: '2025-08-01T15:29:52.252Z', contentLength: 289, hasPaywallMarker: true, postData: {…}, hasPaywallInfo: false, …}contentLength: 289hasPaywallInfo: falsehasPaywallMarker: trueloading: falsepaywallVariant: "default"postData: isUnlocked: truerequiredMemberLevel: 4title: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"unlockPrice: 0.02[[Prototype]]: Objecttimestamp: "2025-08-01T15:29:52.252Z"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Component rendered for postId: 25158 {timestamp: '2025-08-01T15:29:52.252Z', contentLength: 289, hasPaywallMarker: true, unlockPrice: 0.02, requiredMemberLevel: 4, …}contentLength: 289hasPaywallInfo: falsehasPaywallMarker: trueisUnlocked: truelegacyMode: falsepaywallInfoHasPaywall: undefinedrequiredMemberLevel: 4showLoading: falsetimestamp: "2025-08-01T15:29:52.252Z"unlockPrice: 0.02userMemberLevel: 3variant: "default"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Traditional mode detection for postId: 25158 {timestamp: '2025-08-01T15:29:52.253Z', legacyMode: false, hasPaywall: true, contentHasMarker: true, paywallInfoHasPaywall: undefined, …}contentHasMarker: truedisplayContentLength: 289hasPaywall: truelegacyMode: falselevelLoading: truepaywallInfoHasPaywall: undefinedshowLoading: falsetimestamp: "2025-08-01T15:29:52.253Z"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Showing skeleton screen for postId: 25158 {timestamp: '2025-08-01T15:29:52.253Z', legacyMode: false, hasPaywallMarker: true, reason: 'No preprocessed paywall info but content has paywall marker'}hasPaywallMarker: truelegacyMode: falsereason: "No preprocessed paywall info but content has paywall marker"timestamp: "2025-08-01T15:29:52.253Z"[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] PostData state changed for postId: 25158 {title: '《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈', unlockPrice: 0.02, requiredMemberLevel: 4, isUnlocked: true}isUnlocked: truerequiredMemberLevel: 4title: "《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈"unlockPrice: 0.02[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] PaywallInfo state changed for postId: 25158 null
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Component rendered for postId: 25158 {timestamp: '2025-08-01T15:29:53.075Z', contentLength: 289, hasPaywallMarker: true, unlockPrice: 0.02, requiredMemberLevel: 4, …}contentLength: 289hasPaywallInfo: falsehasPaywallMarker: trueisUnlocked: truelegacyMode: falsepaywallInfoHasPaywall: undefinedrequiredMemberLevel: 4showLoading: falsetimestamp: "2025-08-01T15:29:53.075Z"unlockPrice: 0.02userMemberLevel: 3variant: "default"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Traditional mode detection for postId: 25158 {timestamp: '2025-08-01T15:29:53.075Z', legacyMode: false, hasPaywall: true, contentHasMarker: true, paywallInfoHasPaywall: undefined, …}contentHasMarker: truedisplayContentLength: 289hasPaywall: truelegacyMode: falselevelLoading: falsepaywallInfoHasPaywall: undefinedshowLoading: falsetimestamp: "2025-08-01T15:29:53.075Z"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Showing skeleton screen for postId: 25158 {timestamp: '2025-08-01T15:29:53.076Z', legacyMode: false, hasPaywallMarker: true, reason: 'No preprocessed paywall info but content has paywall marker'}hasPaywallMarker: truelegacyMode: falsereason: "No preprocessed paywall info but content has paywall marker"timestamp: "2025-08-01T15:29:53.076Z"[[Prototype]]: Object



后端日志

 [01-Aug-2025 15:29:18 UTC] [DEBUG] Checking access for post_id: 25158, user_id: 117
[01-Aug-2025 15:29:18 UTC] [DEBUG] Post 25158 requires level_id: 4
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 15:29:18 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: {"id":3,"name":"\u5b63\u4ed8\u4f1a\u5458","description":"\u5b63\u5ea6\u4ed8\u8d39","priority":50,"price":0.02,"duration":3,"duration_unit":"months"}
[01-Aug-2025 15:29:18 UTC] [DEBUG] Final access check for post_id: 25158, user_id: 117. User priority: 50, Required priority: 80. Access: Denied
[01-Aug-2025 15:29:18 UTC] [DEBUG] Checking access for post_id: 25158, user_id: 117
[01-Aug-2025 15:29:18 UTC] [DEBUG] Post 25158 requires level_id: 4
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 15:29:18 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: {"id":3,"name":"\u5b63\u4ed8\u4f1a\u5458","description":"\u5b63\u5ea6\u4ed8\u8d39","priority":50,"price":0.02,"duration":3,"duration_unit":"months"}
[01-Aug-2025 15:29:18 UTC] [DEBUG] Final access check for post_id: 25158, user_id: 117. User priority: 50, Required priority: 80. Access: Denied
[01-Aug-2025 15:29:18 UTC] ==== 相关文章设置调试 ====
[01-Aug-2025 15:29:18 UTC] 文章ID: 25158
[01-Aug-2025 15:29:18 UTC] 文章标题: 《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈
[01-Aug-2025 15:29:18 UTC] fd_related_posts_count选项值: 12
[01-Aug-2025 15:29:18 UTC] GraphQL参数count值: 未设置
[01-Aug-2025 15:29:18 UTC] 最终使用的count值: 12
[01-Aug-2025 15:29:18 UTC] fd_module_settings选项值: Array
(
    [fd_related_posts_strategy] => mixed
    [fd_related_posts_count] => 12
    [fd_posts_per_page] => 12
)

[01-Aug-2025 15:29:18 UTC] ==========================
[01-Aug-2025 15:29:18 UTC] GraphQL relatedPosts使用策略: mixed, 数量: 12
[01-Aug-2025 15:29:18 UTC] ==== 相关文章设置调试 ====
[01-Aug-2025 15:29:18 UTC] 文章ID: 25158
[01-Aug-2025 15:29:18 UTC] 文章标题: 《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈
[01-Aug-2025 15:29:18 UTC] fd_related_posts_count选项值: 12
[01-Aug-2025 15:29:18 UTC] GraphQL参数count值: 未设置
[01-Aug-2025 15:29:18 UTC] 最终使用的count值: 12
[01-Aug-2025 15:29:18 UTC] fd_module_settings选项值: Array
(
    [fd_related_posts_strategy] => mixed
    [fd_related_posts_count] => 12
    [fd_posts_per_page] => 12
)

[01-Aug-2025 15:29:18 UTC] ==========================
[01-Aug-2025 15:29:18 UTC] GraphQL relatedPosts使用策略: mixed, 数量: 12
[01-Aug-2025 15:29:18 UTC] PHP Deprecated:  函数 WPGraphQL\Data\DataSource::resolve_post_object 自版本 0.8.4 起已<strong>弃用</strong>！请使用 Use $context->get_loader( 'post' )->load_deferred( $id ) instead. 代替。 in /var/www/html/wp-includes/functions.php on line 6121
[01-Aug-2025 15:29:18 UTC] [DEBUG-GraphQL-IsUnlocked] Checking unlock status for post_id: 25158, user_id: 117
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 15:29:18 UTC] [DEBUG-GraphQL-IsUnlocked] Unlock status for post_id: 25158, user_id: 117: LOCKED
[01-Aug-2025 15:29:18 UTC] [DEBUG] Checking access for post_id: 25158, user_id: 117
[01-Aug-2025 15:29:18 UTC] [DEBUG] Post 25158 requires level_id: 4
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 15:29:18 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: {"id":3,"name":"\u5b63\u4ed8\u4f1a\u5458","description":"\u5b63\u5ea6\u4ed8\u8d39","priority":50,"price":0.02,"duration":3,"duration_unit":"months"}
[01-Aug-2025 15:29:18 UTC] [DEBUG] Final access check for post_id: 25158, user_id: 117. User priority: 50, Required priority: 80. Access: Denied
[01-Aug-2025 15:29:18 UTC] PHP Deprecated:  函数 WPGraphQL\Data\DataSource::resolve_post_object 自版本 0.8.4 起已<strong>弃用</strong>！请使用 Use $context->get_loader( 'post' )->load_deferred( $id ) instead. 代替。 in /var/www/html/wp-includes/functions.php on line 6121
[01-Aug-2025 15:29:18 UTC] [DEBUG-GraphQL-IsUnlocked] Checking unlock status for post_id: 25158, user_id: 117
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 15:29:18 UTC] [DEBUG-GraphQL-IsUnlocked] Unlock status for post_id: 25158, user_id: 117: LOCKED
[01-Aug-2025 15:29:18 UTC] [DEBUG] Checking access for post_id: 25158, user_id: 117
[01-Aug-2025 15:29:18 UTC] [DEBUG] Post 25158 requires level_id: 4
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:18 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 15:29:18 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: {"id":3,"name":"\u5b63\u4ed8\u4f1a\u5458","description":"\u5b63\u5ea6\u4ed8\u8d39","priority":50,"price":0.02,"duration":3,"duration_unit":"months"}
[01-Aug-2025 15:29:18 UTC] [DEBUG] Final access check for post_id: 25158, user_id: 117. User priority: 50, Required priority: 80. Access: Denied
[01-Aug-2025 15:29:27 UTC] [DEBUG] Checking access for post_id: 25158, user_id: 117
[01-Aug-2025 15:29:27 UTC] [DEBUG] Post 25158 requires level_id: 4
[01-Aug-2025 15:29:27 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:27 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:27 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 15:29:27 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: {"id":3,"name":"\u5b63\u4ed8\u4f1a\u5458","description":"\u5b63\u5ea6\u4ed8\u8d39","priority":50,"price":0.02,"duration":3,"duration_unit":"months"}
[01-Aug-2025 15:29:27 UTC] [DEBUG] Final access check for post_id: 25158, user_id: 117. User priority: 50, Required priority: 80. Access: Denied
[01-Aug-2025 15:29:27 UTC] [DEBUG-GraphQL-IsUnlocked] Checking unlock status for post_id: 25158, user_id: 117
[01-Aug-2025 15:29:27 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:27 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:27 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 15:29:27 UTC] [DEBUG-GraphQL-IsUnlocked] Unlock status for post_id: 25158, user_id: 117: LOCKED
[01-Aug-2025 15:29:27 UTC] [DEBUG] Checking access for post_id: 25158, user_id: 117
[01-Aug-2025 15:29:27 UTC] [DEBUG] Post 25158 requires level_id: 4
[01-Aug-2025 15:29:27 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:27 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:27 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 15:29:27 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: {"id":3,"name":"\u5b63\u4ed8\u4f1a\u5458","description":"\u5b63\u5ea6\u4ed8\u8d39","priority":50,"price":0.02,"duration":3,"duration_unit":"months"}
[01-Aug-2025 15:29:27 UTC] [DEBUG] Final access check for post_id: 25158, user_id: 117. User priority: 50, Required priority: 80. Access: Denied
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] ========== CREATE UNLOCK ORDER START ==========
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] Timestamp: 2025-08-01 23:29:34
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] Input: {"callbackUrl":"https:\/\/www.futuredecade.com\/article\/250413-229279\/ai-china-scale","paymentMethod":"wxpay","postId":"25158"}
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] User ID: 117, Post ID: 25158
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] Required level ID: 4
[01-Aug-2025 15:29:34 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:34 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:34 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] Already unlocked check: NO
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] Unlock price: 0.02
[01-Aug-2025 15:29:34 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: {"id":3,"name":"\u5b63\u4ed8\u4f1a\u5458","description":"\u5b63\u5ea6\u4ed8\u8d39","priority":50,"price":0.02,"duration":3,"duration_unit":"months"}
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] Creating order with parameters:
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] - Title: Unlock post: 《经济学人》| 中国人工智能空前繁荣、规模惊人哈哈哈
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] - Amount: 0.02
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] - User ID: 117
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] - Payment Method: wxpay
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] - Metadata: {"order_type":"post_unlock","post_id":25158,"callbackUrl":"https:\/\/www.futuredecade.com\/article\/250413-229279\/ai-china-scale"}
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] Order creation result: SUCCESS (ID: 181)
[01-Aug-2025 15:29:34 UTC] [DEBUG-GraphQL-CreateUnlockOrder] ========== CREATE UNLOCK ORDER END ==========
[01-Aug-2025 15:29:50 UTC] [FD WebSocket Push DEBUG] Processing payment success event
[01-Aug-2025 15:29:50 UTC] [FD WebSocket Push DEBUG] Sending post unlock notification for user 117, post 25158
[01-Aug-2025 15:29:50 UTC] [FD WebSocket Push DEBUG] Sending WebSocket event: post:unlocked to target: user_117
[01-Aug-2025 15:29:50 UTC] [FD WebSocket Push DEBUG] WebSocket push sent successfully (non-blocking)
[01-Aug-2025 15:29:50 UTC] [DEBUG-Payment-Complete] Triggering fd_payment_order_completed action for order_id: 181, payment_method: wxpay
[01-Aug-2025 15:29:50 UTC] [DEBUG-Payment-Handler] ========== POST UNLOCK PAYMENT HANDLER START ==========
[01-Aug-2025 15:29:50 UTC] [DEBUG-Payment-Handler] Timestamp: 2025-08-01 23:29:50
[01-Aug-2025 15:29:50 UTC] [DEBUG-Payment-Handler] Order ID: 181
[01-Aug-2025 15:29:50 UTC] [DEBUG-Payment-Handler] fd_member_handle_post_unlock_payment triggered for order_id: 181
[01-Aug-2025 15:29:50 UTC] [DEBUG-Payment-Handler] Order found: {"id":"181","user_id":"117","payment_status":"paid","amount":"0.02","product_type":"post_unlock","product_id":"25158"}
[01-Aug-2025 15:29:50 UTC] [DEBUG-Payment-Handler] Raw metadata: {"order_type":"post_unlock","post_id":25158,"callbackUrl":"https:\/\/www.futuredecade.com\/article\/250413-229279\/ai-china-scale"}
[01-Aug-2025 15:29:50 UTC] [DEBUG-Payment-Handler] Parsed metadata: {"order_type":"post_unlock","post_id":25158,"callbackUrl":"https:\/\/www.futuredecade.com\/article\/250413-229279\/ai-china-scale"}
[01-Aug-2025 15:29:50 UTC] [DEBUG-Payment-Handler] Processing unlock for post_id: 25158, user_id: 117, order_id: 181
[01-Aug-2025 15:29:50 UTC] [DEBUG-Payment-Handler] Calling fd_member_record_post_unlock...
[01-Aug-2025 15:29:50 UTC] [DEBUG-DB-Write] fd_member_record_post_unlock called for user_id: 117, post_id: 25158 at 2025-08-01 23:29:50
[01-Aug-2025 15:29:50 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:50 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:50 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 15:29:50 UTC] [DEBUG-DB-Write] Inserting unlock record into fd_unlocked_posts
[01-Aug-2025 15:29:50 UTC] [DEBUG-DB-Write] Successfully inserted unlock record with ID: 2 for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:50 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:50 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:50 UTC] [DEBUG-DB-Check] Query result: 1, Status: UNLOCKED
[01-Aug-2025 15:29:50 UTC] [DEBUG-DB-Write] Verification check after insert: CONFIRMED
[01-Aug-2025 15:29:50 UTC] [DEBUG-Payment-Handler] fd_member_record_post_unlock result for user 117, post 25158: SUCCESS
[01-Aug-2025 15:29:50 UTC] [DEBUG-Payment-Handler] Post unlock completed successfully!
[01-Aug-2025 15:29:50 UTC] [DEBUG-Payment-Handler] ========== POST UNLOCK PAYMENT HANDLER END ==========
[01-Aug-2025 15:29:50 UTC] [DEBUG-Payment-Complete] fd_payment_order_completed action completed for order_id: 181
[01-Aug-2025 15:29:52 UTC] [DEBUG] Checking access for post_id: 25158, user_id: 117
[01-Aug-2025 15:29:52 UTC] [DEBUG] Post 25158 requires level_id: 4
[01-Aug-2025 15:29:52 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:52 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:52 UTC] [DEBUG-DB-Check] Query result: 1, Status: UNLOCKED
[01-Aug-2025 15:29:52 UTC] [DEBUG] Access granted for post_id: 25158, user_id: 117 due to individual unlock.
[01-Aug-2025 15:29:52 UTC] [DEBUG-GraphQL-IsUnlocked] Checking unlock status for post_id: 25158, user_id: 117
[01-Aug-2025 15:29:52 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:52 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:52 UTC] [DEBUG-DB-Check] Query result: 1, Status: UNLOCKED
[01-Aug-2025 15:29:52 UTC] [DEBUG-GraphQL-IsUnlocked] Unlock status for post_id: 25158, user_id: 117: UNLOCKED
[01-Aug-2025 15:29:52 UTC] [DEBUG] Checking access for post_id: 25158, user_id: 117
[01-Aug-2025 15:29:52 UTC] [DEBUG] Post 25158 requires level_id: 4
[01-Aug-2025 15:29:52 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25158
[01-Aug-2025 15:29:52 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25158
[01-Aug-2025 15:29:52 UTC] [DEBUG-DB-Check] Query result: 1, Status: UNLOCKED
[01-Aug-2025 15:29:52 UTC] [DEBUG] Access granted for post_id: 25158, user_id: 117 due to individual unlock.