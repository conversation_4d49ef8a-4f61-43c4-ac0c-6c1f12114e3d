#!/bin/bash

# DEBUG-PAYWALL-INVESTIGATION: 调试日志清理脚本
# 用于清理所有添加的调试代码和日志

echo "=========================================="
echo "清理付费墙调试代码"
echo "=========================================="

# 前端文件列表
FRONTEND_FILES=(
    "fd-frontend/src/components/post/PostContentSmart.tsx"
    "fd-frontend/src/components/post/PaywallRenderer.tsx"
    "fd-frontend/src/components/post/PaywallCard.tsx"
    "fd-frontend/src/components/payment/PaymentProcessor.tsx"
)

# 后端文件列表
BACKEND_FILES=(
    "fd-member/includes/content-access-control/cac-graphql.php"
    "fd-member/includes/content-access-control/cac-db.php"
    "fd-member/includes/content-access-control/cac-core.php"
    "fd-payment/includes/orders.php"
    "fd-payment/payment/wallet.php"
    "fd-payment/payment/points.php"
)

echo "将清理以下文件中的调试代码："
for file in "${FRONTEND_FILES[@]}" "${BACKEND_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✓ $file"
    else
        echo "  ✗ $file (文件不存在)"
    fi
done

echo ""
read -p "确认清理所有调试代码？(y/N): " confirm

if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
    echo ""
    echo "开始清理调试代码..."
    
    # 清理前端文件中的调试代码
    for file in "${FRONTEND_FILES[@]}"; do
        if [ -f "$file" ]; then
            echo "清理 $file..."
            # 删除包含 DEBUG-PAYWALL-INVESTIGATION 或 [DEBUG- 的行
            sed -i.bak '/DEBUG-PAYWALL-INVESTIGATION\|console\.log.*\[DEBUG-/d' "$file"
            # 删除备份文件
            rm -f "$file.bak"
        fi
    done
    
    # 清理后端文件中的调试代码
    for file in "${BACKEND_FILES[@]}"; do
        if [ -f "$file" ]; then
            echo "清理 $file..."
            # 删除包含 DEBUG-PAYWALL-INVESTIGATION 或 error_log.*[DEBUG- 的行
            sed -i.bak '/DEBUG-PAYWALL-INVESTIGATION\|error_log.*\[DEBUG-/d' "$file"
            # 删除备份文件
            rm -f "$file.bak"
        fi
    done
    
    echo ""
    echo "✓ 调试代码清理完成！"
    echo ""
    echo "注意：如果需要恢复，请使用 git checkout 命令。"
else
    echo "取消清理操作。"
fi
