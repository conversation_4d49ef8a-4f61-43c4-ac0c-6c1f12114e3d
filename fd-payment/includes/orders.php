<?php
// 防止直接访问
defined('ABSPATH') || exit;

/**
 * 创建订单表
 */
function fd_payment_create_order_table() {
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    
    // 创建订单表
    $table_name = $wpdb->prefix . 'orders';
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        order_number varchar(50) NOT NULL,
        title varchar(255) NOT NULL,
        description text,
        amount decimal(10,2) NOT NULL,
        user_id bigint(20),
        payment_method varchar(20),
        payment_status varchar(20) DEFAULT 'unpaid',
        product_type varchar(50),
        product_id varchar(50),
        metadata text,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY  (id),
        KEY order_number (order_number),
        KEY payment_status (payment_status)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * 创建订单
 */
function fd_payment_create_order($title, $amount, $user_id, $payment_method, $description = '', $product_type = '', $product_id = '', $metadata = '') {
    global $wpdb;
    $table_name = $wpdb->prefix . 'orders';
    
    // 生成唯一订单号
    $order_number = 'FD' . date('YmdHis') . rand(1000, 9999);
    
    $order_data = array(
        'order_number' => $order_number,
        'title' => $title,
        'description' => $description,
        'amount' => $amount,
        'user_id' => $user_id,
        'payment_method' => $payment_method,
        'payment_status' => 'unpaid',
    );
    
    $format = array('%s', '%s', '%s', '%f', '%d', '%s', '%s');
    
    // 如果提供了产品类型，添加到数据中
    if (!empty($product_type)) {
        $order_data['product_type'] = $product_type;
        $format[] = '%s';
    }
    
    // 如果提供了产品ID，添加到数据中
    if (!empty($product_id)) {
        $order_data['product_id'] = $product_id;
        $format[] = '%s';
    }
    
    // 如果提供了元数据，添加到数据中
    if (!empty($metadata)) {
        $order_data['metadata'] = $metadata;
        $format[] = '%s';
    }
    
    $wpdb->insert(
        $table_name,
        $order_data,
        $format
    );
    
    return $wpdb->insert_id;
}

/**
 * 获取订单
 */
function fd_payment_get_order($id, $by = 'id') {
    global $wpdb;
    $table_name = $wpdb->prefix . 'orders';
    
    if ($by === 'order_number') {
        return $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE order_number = %s", $id));
    } else {
        return $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id));
    }
}

/**
 * 更新订单状态
 */
function fd_payment_update_order_status($order_id, $status) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'orders';
    
    return $wpdb->update(
        $table_name,
        array('payment_status' => $status),
        array('id' => $order_id),
        array('%s'),
        array('%d')
    );
}

/**
 * 获取所有订单
 */
function fd_payment_get_orders($args = array()) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'orders';
    
    // 默认参数
    $defaults = array(
        'number' => 20,
        'offset' => 0,
        'payment_method' => '',
        'payment_status' => '',
        'orderby' => 'id',
        'order' => 'DESC',
    );
    
    $args = wp_parse_args($args, $defaults);
    
    // 构建查询
    $where = array();
    $where_values = array();
    
    if (!empty($args['payment_method'])) {
        $where[] = 'payment_method = %s';
        $where_values[] = $args['payment_method'];
    }
    
    if (!empty($args['payment_status'])) {
        $where[] = 'payment_status = %s';
        $where_values[] = $args['payment_status'];
    }
    
    $where_sql = '';
    if (!empty($where)) {
        $where_sql = 'WHERE ' . implode(' AND ', $where);
        $where_sql = $wpdb->prepare($where_sql, $where_values);
    }
    
    // 排序
    $orderby = sanitize_sql_orderby($args['orderby'] . ' ' . $args['order']);
    
    // 限制
    $limit = '';
    if (!empty($args['number'])) {
        $limit = $wpdb->prepare('LIMIT %d, %d', $args['offset'], $args['number']);
    }
    
    // 执行查询
    $sql = "SELECT * FROM $table_name $where_sql ORDER BY $orderby $limit";
    
    return $wpdb->get_results($sql);
}

/**
 * 计算订单总数
 */
function fd_payment_count_orders($args = array()) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'orders';
    
    // 构建查询
    $where = array();
    $where_values = array();
    
    if (!empty($args['payment_method'])) {
        $where[] = 'payment_method = %s';
        $where_values[] = $args['payment_method'];
    }
    
    if (!empty($args['payment_status'])) {
        $where[] = 'payment_status = %s';
        $where_values[] = $args['payment_status'];
    }
    
    $where_sql = '';
    if (!empty($where)) {
        $where_sql = 'WHERE ' . implode(' AND ', $where);
        $where_sql = $wpdb->prepare($where_sql, $where_values);
    }
    
    // 执行查询
    $sql = "SELECT COUNT(*) FROM $table_name $where_sql";
    
    return $wpdb->get_var($sql);
}

/**
 * 订单支付完成
 */
function fd_payment_complete_order($order_id, $payment_method) {
    global $wpdb;
    
    // 更新订单状态
    $updated = fd_payment_update_order_status($order_id, 'paid');
    
    if ($updated) {
        // 记录日志
        fd_payment_log("订单 #$order_id 支付完成", $payment_method, 'info', $order_id);
        
        // 获取订单详情
        $order = fd_payment_get_order($order_id);
        if ($order) {
            // 准备支付成功的数据
            $payment_data = array(
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'title' => $order->title,
                'description' => $order->description,
                'amount' => $order->amount,
                'user_id' => $order->user_id,
                'payment_method' => $order->payment_method,
                'product_type' => $order->product_type,
                'product_id' => $order->product_id,
                'metadata' => $order->metadata,
            );
            
            // 执行支付成功动作，兼容旧版钩子
            do_action('fd_payment_success', $payment_data);
            
            // 如果是会员等级订单，直接调用会员等级更新功能
            if ($order->product_type === 'member_level' && function_exists('fd_process_member_payment_success')) {
                // 记录日志
                fd_payment_log("尝试直接更新用户会员等级", $payment_method, 'info', $order_id);
                
                // 直接调用会员等级更新函数
                fd_process_member_payment_success($payment_data);
            }
        }
        
        // 执行支付完成的动作
        error_log("[DEBUG-Payment-Complete] Triggering fd_payment_order_completed action for order_id: {$order_id}, payment_method: {$payment_method}");
        do_action('fd_payment_order_completed', $order_id, $payment_method);
        error_log("[DEBUG-Payment-Complete] fd_payment_order_completed action completed for order_id: {$order_id}");
    }
    
    return $updated;
}

/**
 * 添加订单管理菜单
 */
add_action('admin_menu', 'fd_payment_add_orders_menu');
function fd_payment_add_orders_menu() {
    add_submenu_page(
        'fd-payment-settings',
        '订单管理',
        '订单管理',
        'manage_options',
        'fd-payment-orders',
        'fd_payment_orders_page_callback'
    );
    
    add_submenu_page(
        'fd-payment-settings',
        '支付测试',
        '支付测试',
        'manage_options',
        'fd-payment-test',
        'fd_payment_test_page_callback'
    );
}

/**
 * 订单管理页面回调
 */
function fd_payment_orders_page_callback() {
    // 处理订单状态更新
    if (isset($_GET['action']) && $_GET['action'] === 'update_status' && isset($_GET['order_id']) && isset($_GET['status'])) {
        if (!isset($_GET['_wpnonce']) || !wp_verify_nonce($_GET['_wpnonce'], 'fd_update_order_status')) {
            wp_die('安全校验失败');
        }
        
        $order_id = intval($_GET['order_id']);
        $status = sanitize_text_field($_GET['status']);
        
        fd_payment_update_order_status($order_id, $status);
        
        // 重定向，避免刷新重复提交
        wp_redirect(admin_url('admin.php?page=fd-payment-orders&updated=1'));
        exit;
    }
    
    // 分页参数
    $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
    $per_page = 20;
    
    // 筛选参数
    $payment_method = isset($_GET['payment_method']) ? sanitize_text_field($_GET['payment_method']) : '';
    $payment_status = isset($_GET['payment_status']) ? sanitize_text_field($_GET['payment_status']) : '';
    
    // 查询参数
    $args = array(
        'number' => $per_page,
        'offset' => ($current_page - 1) * $per_page,
        'payment_method' => $payment_method,
        'payment_status' => $payment_status,
    );
    
    // 获取订单数据
    $orders = fd_payment_get_orders($args);
    $total_orders = fd_payment_count_orders(array(
        'payment_method' => $payment_method,
        'payment_status' => $payment_status,
    ));
    
    // 计算总页数
    $total_pages = ceil($total_orders / $per_page);
    
    // 页面输出
    ?>
    <div class="wrap">
        <h1 class="wp-heading-inline">订单管理</h1>
        
        <?php if (isset($_GET['updated']) && $_GET['updated'] == 1): ?>
            <div class="notice notice-success is-dismissible">
                <p>订单状态已更新。</p>
            </div>
        <?php endif; ?>
        
        <form method="get">
            <input type="hidden" name="page" value="fd-payment-orders">
            
            <div class="tablenav top">
                <div class="alignleft actions">
                    <select name="payment_method">
                        <option value="">所有支付方式</option>
                        <option value="alipay" <?php selected($payment_method, 'alipay'); ?>>支付宝</option>
                    </select>
                    
                    <select name="payment_status">
                        <option value="">所有状态</option>
                        <option value="unpaid" <?php selected($payment_status, 'unpaid'); ?>>未支付</option>
                        <option value="paid" <?php selected($payment_status, 'paid'); ?>>已支付</option>
                    </select>
                    
                    <?php submit_button('筛选', 'action', '', false); ?>
                </div>
                
                <div class="tablenav-pages">
                    <span class="displaying-num"><?php echo sprintf('共 %s 个订单', number_format_i18n($total_orders)); ?></span>
                    <?php
                    // 分页链接
                    $page_links = paginate_links(array(
                        'base' => add_query_arg('paged', '%#%'),
                        'format' => '',
                        'prev_text' => '&laquo;',
                        'next_text' => '&raquo;',
                        'total' => $total_pages,
                        'current' => $current_page,
                    ));
                    
                    if ($page_links) {
                        echo '<span class="pagination-links">' . $page_links . '</span>';
                    }
                    ?>
                </div>
            </div>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>订单号</th>
                        <th>商品</th>
                        <th>金额</th>
                        <th>用户</th>
                        <th>支付方式</th>
                        <th>支付状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($orders): ?>
                        <?php foreach ($orders as $order): ?>
                            <tr>
                                <td><?php echo esc_html($order->id); ?></td>
                                <td><?php echo esc_html($order->order_number); ?></td>
                                <td><?php echo esc_html($order->title); ?></td>
                                <td><?php echo esc_html(number_format($order->amount, 2)); ?></td>
                                <td>
                                    <?php 
                                    if ($order->user_id) {
                                        $user = get_user_by('ID', $order->user_id);
                                        if ($user) {
                                            echo esc_html($user->display_name);
                                        } else {
                                            echo "用户#" . esc_html($order->user_id);
                                        }
                                    } else {
                                        echo "游客";
                                    }
                                    ?>
                                </td>
                                <td><?php echo esc_html($order->payment_method); ?></td>
                                <td>
                                    <?php if ($order->payment_status === 'paid'): ?>
                                        <span style="color: green;">已支付</span>
                                    <?php else: ?>
                                        <span style="color: red;">未支付</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo esc_html($order->created_at); ?></td>
                                <td>
                                    <?php if ($order->payment_status === 'unpaid'): ?>
                                        <a href="<?php echo esc_url(wp_nonce_url(add_query_arg(array('action' => 'update_status', 'order_id' => $order->id, 'status' => 'paid'), admin_url('admin.php?page=fd-payment-orders')), 'fd_update_order_status')); ?>" class="button button-small">标记为已支付</a>
                                        <a href="<?php echo esc_url(admin_url('admin-ajax.php?action=fd_checkout_payment&order_id=' . $order->id)); ?>" class="button button-small" target="_blank">去支付</a>
                                    <?php else: ?>
                                        <a href="<?php echo esc_url(wp_nonce_url(add_query_arg(array('action' => 'update_status', 'order_id' => $order->id, 'status' => 'unpaid'), admin_url('admin.php?page=fd-payment-orders')), 'fd_update_order_status')); ?>" class="button button-small">标记为未支付</a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="9">没有找到订单</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </form>
    </div>
    <?php
}

/**
 * 支付测试页面回调
 */
function fd_payment_test_page_callback() {
    ?>
    <div class="wrap">
        <h1>支付测试</h1>
        <form method="post" action="<?php echo esc_url(admin_url('admin-ajax.php?action=fd_create_test_order')); ?>">
            <?php wp_nonce_field('fd_payment_test_nonce'); ?>
            <table class="form-table">
                <tr>
                    <th scope="row">商品名称</th>
                    <td><input type="text" name="title" value="测试商品" class="regular-text"></td>
                </tr>
                <tr>
                    <th scope="row">金额</th>
                    <td><input type="text" name="amount" value="0.01" class="regular-text"></td>
                </tr>
                <tr>
                    <th scope="row">支付方式</th>
                    <td>
                        <select name="payment_method">
                            <option value="alipay">支付宝</option>
                            <option value="wxpay">微信支付</option>
                        </select>
                    </td>
                </tr>
            </table>
            <?php submit_button('创建测试订单'); ?>
        </form>
    </div>
    <?php
} 