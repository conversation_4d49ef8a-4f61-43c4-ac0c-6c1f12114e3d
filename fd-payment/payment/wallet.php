<?php
namespace FD\Payment\Payments;

defined('ABSPATH') || exit;

class Wallet extends Payment {
    public function __construct() {
        $this->id = 'wallet';
        $this->title = '钱包余额';
        $this->modal = true;
        $this->icon = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 18"><path d="M20.005 3h-7a6 6 0 1 0 0 12h7v2a1 1 0 0 1-1 1h-18a1 1 0 0 1-1-1V1a1 1 0 0 1 1-1h18a1 1 0 0 1 1 1v2zm-7 2h8v8h-8a4 4 0 1 1 0-8zm0 3v2h3V8h-3z"/></svg>';
        $this->color = 'var(--member-color,var(--theme-color,#206be7))';

        add_action('wp_ajax_fd_wallet_pay', array($this, 'paynow'));
        add_action('wp_ajax_nopriv_fd_wallet_pay', array($this, 'paynow'));
        parent::__construct();
    }

    function add_gateway($methods) {
        $user = wp_get_current_user();
        if($this->id && $user && isset($user->ID) && $user->ID){
            $methods[$this->id] = $this;
        }
        return $methods;
    }

    public function process_payment($order_id) {
        $url = $this->get_checkout_payment_url($order_id);
        return array(
            'result' => 'success',
            'redirect' =>  $url,
            'height' => '320px',
            'modal-size' => 'modal-sm'
        );
    }

    public function receipt_page($order_id) {
        // 获取订单信息
        $order = fd_payment_get_order($order_id);
        if (!$order || $order->payment_status === 'paid') return;
        
        $user = wp_get_current_user();
        if($user && isset($user->ID) && $user->ID) {
            $balance = $this->get_balance();
            if(is_numeric($balance)) {
                $price = $order->amount;
                if(is_numeric($price) && $price > 0) {
                    if($balance >= $price) { ?>
                        <div style="color: #666;font-size: 14px;line-height: 1;">可用余额<span style="display: block;margin-top: 6px;font-size: 24px;font-weight:500;color: #f76142;">￥<?php echo $balance;?></span></div>
                        <button type="button" class="btn btn-view btn-pay">立即支付</button>
                        <script src="<?php echo includes_url('js/jquery/jquery.min.js'); ?>"></script>
                        <script>
                            _ajax_url = "<?php echo admin_url( 'admin-ajax.php');?>";
                            _nonce = "<?php echo wp_create_nonce('fd_wallet_receipt_page');?>";
                            jQuery(function ($) {
                                $(document.body).on('click', '.btn', function(){
                                    var $btn = $(this);
                                    if($btn.hasClass('loading')) return false;
                                    $btn.addClass('loading').html('支付中，请稍候...');
                                    jQuery.ajax({
                                        type: 'POST',
                                        url: _ajax_url,
                                        dataType: 'json',
                                        data: {
                                            order_id: <?php echo $order_id; ?>,
                                            action: 'fd_wallet_pay',
                                            nonce: _nonce
                                        }
                                    }).done(function (data) {
                                        $btn.removeClass('loading').html('立即支付');
                                        if (data && data.redirect) {
                                            location.href = data.redirect;
                                            if(window.parent) window.parent.document.getElementById('fd-pay-iframe').style.height = '460px'
                                        } else {
                                            alert('支付异常，请稍候再试或者联系管理员获取帮助');
                                        }
                                    });
                                });
                            })
                        </script>
                    <?php } else {
                        $wallet_url = function_exists('fd_subpage_url') ? fd_subpage_url('wallet') : admin_url('profile.php');
                        echo '<div class="text-center" style="color:#f66;"><p style="margin-bottom: .3em;">钱包余额不足！</p>请先充值或者使用用其他支付方式进行支付。</div><a class="btn btn-view" target="_blank" href="' . esc_url($wallet_url) . '">进入我的钱包充值 <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="128" height="128"><path d="M690.005333 469.333333l-228.864-228.864 60.330667-60.330666L853.333333 512l-331.861333 331.861333-60.330667-60.330666L690.005333 554.666667H170.666667v-85.333334z"></path></svg></a>';
                    }
                } else {
                    echo '<div class="text-center" style="color:#f66;">订单价格异常</div>';
                }
            } else {
                echo '<div class="text-center" style="color:#f66;">余额获取失败</div>';
            }
        } else {
            echo '<div class="text-center" style="color:#f66;">请登录后再试</div>';
        }
    }

    function paynow() {
        $res = array('result' => -1);
        $order_id = isset($_POST['order_id']) && $_POST['order_id'] ? $_POST['order_id'] : '';
        $nonce = isset($_POST['nonce']) && $_POST['nonce'] ? $_POST['nonce'] : '';
        
        if($nonce && wp_verify_nonce($nonce, 'fd_wallet_receipt_page')) {
            $order = $order_id ? fd_payment_get_order($order_id) : '';
            $user_id = get_current_user_id();
            
            if ($order && isset($order->id)) {
                $res['redirect'] = $this->get_checkout_return_url($order->id);
                self::add_log('#'.$order->order_number.' 提交余额支付请求', $this->id, $order->id);
            }
            
            if($user_id && $order && isset($order->id) && $order->order_number && (int) $order->user_id === $user_id) {
                $balance = $this->get_balance();
                if(is_numeric($balance)) {
                    $price = $order->amount;
                    if(is_numeric($price) && $price > 0 && $balance >= $price && $order->payment_status === 'unpaid') {
                        $title = $this->get_order_title($order);
                        
                        // 检查是否存在Wallet类，否则使用自定义余额处理函数
                        if(class_exists('\\FD\\Member\\Wallet')) {
                            $use_balance = \FD\Member\Wallet::use_balance(-($price), $user_id, $title);
                        } else {
                            $use_balance = $this->use_balance($price, $user_id, $title);
                        }
                        
                        if($use_balance) {
                            error_log("[DEBUG-Wallet-Payment] Balance payment successful for order_id: {$order->id}, user_id: {$user_id}");
                            $res['result'] = 0;
                            fd_payment_complete_order($order->id, $this->id);
                            self::add_log('#'.$order->order_number.' 订单支付完成！', $this->id, $order->id);
                            error_log("[DEBUG-Wallet-Payment] Order completion triggered for order_id: {$order->id}");
                        } else {
                            error_log("[DEBUG-Wallet-Payment] Balance payment failed for order_id: {$order->id}, user_id: {$user_id}");
                        }
                    }
                }
            }
        }
        
        wp_send_json($res);
    }

    /**
     * 使用余额
     * 如果未安装FD主题或会员插件，则使用此函数处理余额
     */
    function use_balance($amount, $user_id, $title = '') {
        if(!$user_id || !is_numeric($amount)) return false;

        $balance = $this->get_balance();
        $new_balance = $balance - $amount;
        
        if($new_balance >= 0) {
            update_user_option($user_id, '_fd_balance', $new_balance);
            
            // 记录余额变动日志
            $this->add_balance_log($user_id, -$amount, $balance, $new_balance, $title);
            
            return true;
        }
        
        return false;
    }

    /**
     * 记录余额变动日志
     */
    function add_balance_log($user_id, $amount, $from, $to, $title = '') {
        global $wpdb;
        
        $table = $wpdb->prefix . 'balance_log';
        
        // 检查表是否存在，不存在则创建
        if($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
            $this->create_balance_log_table();
        }
        
        $res = $wpdb->insert(
            $table,
            array(
                'user_id' => $user_id,
                'amount' => $amount,
                'from' => $from,
                'to' => $to,
                'desc' => $title,
                'time' => current_time('mysql')
            ),
            array('%d', '%f', '%f', '%f', '%s', '%s')
        );
        
        return $res;
    }
    
    /**
     * 创建余额变动日志表
     */
    function create_balance_log_table() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();
        
        $table = $wpdb->prefix . 'balance_log';
        
        $sql = "CREATE TABLE $table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            amount decimal(10,2) NOT NULL,
            `from` decimal(10,2) NOT NULL,
            `to` decimal(10,2) NOT NULL,
            `desc` varchar(255) NOT NULL,
            time datetime NOT NULL,
            PRIMARY KEY (id),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    function get_balance() {
        $user = wp_get_current_user();
        if($user && isset($user->ID) && $user->ID) {
            $balance = get_user_option('_fd_balance', $user->ID);
            if($balance === '' || $balance === false) $balance = '0.00';
            return $balance;
        }
        return 0;
    }
    
    public static function add_log($log, $type, $order_id = 0) {
        if(function_exists('fd_payment_log')) {
            fd_payment_log($log, $type, $order_id);
        }
    }
}

new Wallet(); 