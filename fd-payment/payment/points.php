<?php
namespace FD\Payment\Payments;

defined('ABSPATH') || exit;

class Points extends Payment {
    public function __construct() {
        $this->id = 'points';
        $this->title = '积分支付';
        $this->modal = true;
        $this->icon = '<svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="128" height="128"><path d="M436.636 385.155c107.161 0 218.624-23.568 285.558-68.505v81.809h62.01V219.33c0-107.678-179.02-165.825-347.46-165.825-168.449 0-347.577 58.147-347.577 165.832v488.886c0 107.685 179.028 165.825 347.469 165.825v-61.903c-176.928 0-285.566-60.548-285.566-103.915V561.258c66.949 44.836 178.297 68.512 285.566 68.512v-62.024c-176.928 0-285.566-60.541-285.566-103.908V316.765c66.949 44.822 178.411 68.39 285.566 68.39zm0-269.732c176.935 0 285.558 60.548 285.558 103.915 0 43.366-108.63 103.914-285.558 103.914S151.07 262.704 151.07 219.338c.014-43.367 108.738-103.922 285.566-103.922zm270.642 310.84c-153.883 0-234.336 59.194-234.336 117.642v314.159c0 58.448 80.554 117.634 234.336 117.634 152.736 0 233.075-58.348 234.322-116.38h.114V543.912c-.107-58.455-80.56-117.648-234.436-117.648zm0 61.91c113.756 0 172.426 39.066 172.426 55.732 0 16.658-58.663 55.724-172.426 55.724s-172.426-39.066-172.426-55.724 58.662-55.731 172.426-55.731zm0 425.615c-113.764 0-172.426-39.066-172.426-55.724v-75.006c39.28 21.375 97.004 35.625 172.426 35.625 75.414 0 133.038-14.243 172.426-35.625v75.006c0 16.658-58.663 55.724-172.426 55.724zm0-157.123c-113.764 0-172.426-39.087-172.426-55.73v-75.007c39.28 21.375 97.004 35.61 172.426 35.61 75.414 0 133.038-14.235 172.426-35.61v75.006c0 16.644-58.663 55.724-172.426 55.724z"/></svg>';
        $this->color = 'var(--member-color,var(--theme-color,#206be7))';

        add_action('wp_ajax_fd_points_pay', [$this, 'paynow']);
        add_action('wp_ajax_nopriv_fd_points_pay', [$this, 'paynow']);
        parent::__construct();
    }

    function add_gateway($methods) {
        $user = wp_get_current_user();
        if($this->id && $user && isset($user->ID) && $user->ID){
            $methods[$this->id] = $this;
        }
        return $methods;
    }

    public function process_payment($order_id) {
        $url = $this->get_checkout_payment_url($order_id);
        return [
            'result' => 'success',
            'redirect' => $url,
            'height' => '320px',
            'modal-size' => 'modal-sm'
        ];
    }

    public function receipt_page($order_id) {
        // 获取订单信息
        $order = fd_payment_get_order($order_id);
        if (!$order || $order->payment_status === 'paid') return;
        
        $user = wp_get_current_user();
        if($user && isset($user->ID) && $user->ID) {
            $points = $this->get_points();

            if(is_numeric($points)) {
                $price = $order->amount;
                if(is_numeric($price) && $price > 0) {
                    if($points >= $price) { ?>
                        <div style="color: #666;font-size: 14px;line-height: 1;">可用积分<span style="display: flex;align-items:center;justify-content: center;margin-top: 6px; gap: 2px;font-size: 24px;font-weight:500;color: #f76142;"><span style="font-size: .9em;"><?php echo $this->icon;?></span><?php echo $points;?></span></div>
                        <button type="button" class="btn btn-view btn-pay">立即支付</button>
                        <script src="<?php echo includes_url('js/jquery/jquery.min.js'); ?>"></script>
                        <script>
                            _ajax_url = "<?php echo admin_url( 'admin-ajax.php');?>";
                            _nonce = "<?php echo wp_create_nonce('fd_points_receipt_page');?>";
                            jQuery(function ($) {
                                $(document.body).on('click', '.btn', function(){
                                    var $btn = $(this);
                                    if($btn.hasClass('loading')) return false;
                                    $btn.addClass('loading').html('支付中，请稍候...');
                                    jQuery.ajax({
                                        type: 'POST',
                                        url: _ajax_url,
                                        dataType: 'json',
                                        data: {
                                            order_id: <?php echo $order_id; ?>,
                                            action: 'fd_points_pay',
                                            nonce: _nonce
                                        }
                                    }).done(function (data) {
                                        $btn.removeClass('loading').html('立即支付');
                                        if (data && data.redirect) {
                                            location.href = data.redirect;
                                            if(window.parent) window.parent.document.getElementById('fd-pay-iframe').style.height = '460px'
                                        } else {
                                            alert('支付异常，请稍候再试或者联系管理员获取帮助');
                                        }
                                    });
                                });
                            })
                        </script>
                    <?php } else {
                        $points_url = function_exists('fd_subpage_url') ? fd_subpage_url('wallet') : admin_url('profile.php');
                        echo '<div class="text-center" style="color:#f66;"><p style="margin-bottom: .3em;">可用积分不足！</p>请确保账户有足够的积分进行支付。</div><a class="btn btn-view" target="_blank" href="' . esc_url($points_url) . '">查看我的积分详情 <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5742" width="128" height="128"><path d="M690.005333 469.333333l-228.864-228.864 60.330667-60.330666L853.333333 512l-331.861333 331.861333-60.330667-60.330666L690.005333 554.666667H170.666667v-85.333334z" p-id="5743"></path></svg></a>';
                    }
                } else {
                    echo '<div class="text-center" style="color:#f66;">订单价格异常</div>';
                }
            } else {
                echo '<div class="text-center" style="color:#f66;">积分获取失败</div>';
            }
        } else {
            echo '<div class="text-center" style="color:#f66;">请登录后再试</div>';
        }
    }

    function paynow() {
        $res = ['result' => -1];
        $order_id = isset($_POST['order_id']) && $_POST['order_id'] ? $_POST['order_id'] : '';
        $nonce = isset($_POST['nonce']) && $_POST['nonce'] ? $_POST['nonce'] : '';
        
        if($nonce && wp_verify_nonce($nonce, 'fd_points_receipt_page')) {
            $order = $order_id ? fd_payment_get_order($order_id) : '';
            $user_id = get_current_user_id();
            
            if ($order && isset($order->id)) {
                $res['redirect'] = $this->get_checkout_return_url($order->id);
                self::add_log('#'.$order->order_number.' 提交积分支付请求', $this->id, $order->id);
            }
            
            if($user_id && $order && isset($order->id) && $order->order_number && (int) $order->user_id === $user_id) {
                $points = $this->get_points();
                if(is_numeric($points)) {
                    $price = $order->amount;
                    if(is_numeric($price) && $price > 0 && $points >= $price && $order->payment_status === 'unpaid') {
                        $title = $this->get_order_title($order);
                        
                        // 检查是否存在Points类，否则使用自定义积分处理函数
                        if(class_exists('\\FD\\Member\\Points')) {
                            $use_points = \FD\Member\Points::use_points(-($price), $user_id, $title);
                        } else {
                            $use_points = $this->use_points($price, $user_id, $title);
                        }
                        
                        if($use_points) {
                            error_log("[DEBUG-Points-Payment] Points payment successful for order_id: {$order->id}, user_id: {$user_id}");
                            $res['result'] = 0;
                            fd_payment_complete_order($order->id, $this->id);
                            self::add_log('#'.$order->order_number.' 订单支付完成！', $this->id, $order->id);
                            error_log("[DEBUG-Points-Payment] Order completion triggered for order_id: {$order->id}");
                        } else {
                            error_log("[DEBUG-Points-Payment] Points payment failed for order_id: {$order->id}, user_id: {$user_id}");
                        }
                    }
                }
            }
        }
        
        wp_send_json($res);
    }

    /**
     * 使用积分
     * 如果未安装FD主题或会员插件，则使用此函数处理积分
     */
    function use_points($amount, $user_id, $title = '') {
        if(!$user_id || !is_numeric($amount)) return false;

        $points = $this->get_points();
        $new_points = $points - $amount;
        
        if($new_points >= 0) {
            update_user_option($user_id, '_fd_points', $new_points);
            
            // 记录积分变动日志
            $this->add_points_log($user_id, -$amount, $points, $new_points, $title);
            
            return true;
        }
        
        return false;
    }

    /**
     * 记录积分变动日志
     */
    function add_points_log($user_id, $amount, $from, $to, $title = '') {
        global $wpdb;
        
        $table = $wpdb->prefix . 'points_log';
        
        // 检查表是否存在，不存在则创建
        if($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
            $this->create_points_log_table();
        }
        
        $res = $wpdb->insert(
            $table,
            array(
                'user_id' => $user_id,
                'points' => $amount,
                'from' => $from,
                'to' => $to,
                'desc' => $title,
                'time' => current_time('mysql')
            ),
            array('%d', '%f', '%f', '%f', '%s', '%s')
        );
        
        return $res;
    }
    
    /**
     * 创建积分变动日志表
     */
    function create_points_log_table() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();
        
        $table = $wpdb->prefix . 'points_log';
        
        $sql = "CREATE TABLE $table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            points decimal(10,2) NOT NULL,
            `from` decimal(10,2) NOT NULL,
            `to` decimal(10,2) NOT NULL,
            `desc` varchar(255) NOT NULL,
            time datetime NOT NULL,
            PRIMARY KEY (id),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    function get_points() {
        $user = wp_get_current_user();
        if($user && isset($user->ID) && $user->ID) {
            $points = get_user_option('_fd_points', $user->ID);
            return $points && $points > 0 ? $points : 0;
        }
        return 0;
    }
    
    public static function add_log($log, $type, $order_id = 0) {
        if(function_exists('fd_payment_log')) {
            fd_payment_log($log, $type, $order_id);
        }
    }
}

new Points(); 