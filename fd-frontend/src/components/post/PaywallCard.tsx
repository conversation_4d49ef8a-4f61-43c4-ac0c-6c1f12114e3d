'use client';

import React, { useState } from 'react';
import { Lock, Crown, CreditCard, User, ArrowRight, Sparkles } from 'lucide-react';
import { useAuthContext } from '@/hooks/useAuthContext';
import Link from 'next/link';
import { useMutation } from '@apollo/client';
import { CREATE_UNLOCK_ORDER } from '@/lib/graphql/mutations';
import PaymentMethodSelector from '../payment/PaymentMethodSelector';
import PaymentProcessor from '../payment/PaymentProcessor';
import BalancePaymentForm from '../payment/BalancePaymentForm';
import { MemberLevel } from '@/types/user-types';

type PaymentStep = 'initial' | 'selecting_payment' | 'creating_order' | 'processing_payment' | 'success';

interface PaywallCardProps {
  // 文章信息
  postId: number;
  postTitle: string;
  
  // 权限信息
  requiredMemberLevel?: number;
  requiredMemberLevelInfo?: MemberLevel;
  unlockPrice?: number;
  isUnlocked?: boolean;
  
  // 用户状态
  userMemberLevel?: MemberLevel | null;
  
  // 链接配置
  loginUrl?: string;
  registerUrl?: string;
  upgradeUrl?: string;
  
  // 回调函数
  onUnlock?: () => void;
}

const PaywallCard: React.FC<PaywallCardProps> = ({
  postId,
  postTitle,
  requiredMemberLevel = 0,
  requiredMemberLevelInfo,
  unlockPrice = 0,
  isUnlocked = false,
  userMemberLevel,
  loginUrl = '/auth/login',
  registerUrl = '/auth/register',
  upgradeUrl = '/membership/upgrade',
  onUnlock
}) => {
  const { isAuthenticated } = useAuthContext();
  const [step, setStep] = useState<PaymentStep>('initial');
  const [orderId, setOrderId] = useState<string | null>(null);
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const [createUnlockOrder, { loading: isCreatingOrder, error }] = useMutation(CREATE_UNLOCK_ORDER);

  // 判断是否应该显示"开通会员"还是"升级会员"
  const shouldShowUpgrade = () => {
    // 如果用户未登录，显示登录选项
    if (!isAuthenticated) {
      return false;
    }

    // 如果用户已登录但没有会员等级，显示"开通会员"
    if (!userMemberLevel) {
      return false;
    }

    // 如果用户有会员等级，显示"升级会员"
    return true;
  };

  const getMembershipButtonText = () => {
    if (!isAuthenticated) {
      return '登录查看';
    }

    if (!userMemberLevel) {
      return '开通会员';
    }

    return '升级会员';
  };

  // 获取等级显示信息
  const getLevelDisplayInfo = () => {
    if (requiredMemberLevelInfo) {
      return {
        name: requiredMemberLevelInfo.name,
        tier: requiredMemberLevelInfo.tier || '会员',
        description: requiredMemberLevelInfo.description || `${requiredMemberLevelInfo.name}专享内容`
      };
    }

    // 如果没有等级信息，使用默认显示
    return {
      name: '会员',
      tier: '会员',
      description: '会员专享内容'
    };
  };

  // 如果已解锁，不显示付费墙
  if (isUnlocked) {
    return null;
  }

  // 步骤1: 用户点击"立即解锁"，切换到支付方式选择界面
  const handleUnlockClick = () => {
    setErrorMessage(null);
    setStep('selecting_payment');
  };

  // 步骤2: 用户选好方式后，点击"确认支付"，此时创建订单
  const handleCreateOrderAndPay = async () => {
    if (!selectedMethod) {
      setErrorMessage('请选择一种支付方式');
      return;
    }
    setErrorMessage(null);
    setStep('creating_order'); // 进入订单创建中状态

    try {
      const { data } = await createUnlockOrder({
        variables: {
          postId: String(postId),
          paymentMethod: selectedMethod,
          callbackUrl: typeof window !== 'undefined' ? window.location.href : ''
        }
      });

      if (data.createUnlockOrder.success && data.createUnlockOrder.orderId) {
        setOrderId(data.createUnlockOrder.orderId);
        setStep('processing_payment'); // 订单创建成功，进入支付处理
      } else {
        throw new Error(data.createUnlockOrder.message || '创建订单失败');
      }
    } catch (e: any) {
      console.error('创建解锁订单失败:', e);
      setErrorMessage(e.message || '发生未知错误，请稍后再试。');
      setStep('selecting_payment'); // 创建失败，退回到选择步骤
    }
  };

  const handlePaymentSuccess = () => {
    setStep('success');
    if (onUnlock) {
      console.log('[PaywallCard] Payment success, calling onUnlock...');
      onUnlock();
    }
  }

  // 获取当前页面URL作为回调地址
  const currentUrl = typeof window !== 'undefined' ? window.location.href : '';
  const loginUrlWithCallback = `${loginUrl}?callbackUrl=${encodeURIComponent(currentUrl)}`;
  const registerUrlWithCallback = `${registerUrl}?callbackUrl=${encodeURIComponent(currentUrl)}`;
  const upgradeUrlWithCallback = `${upgradeUrl}?returnUrl=${encodeURIComponent(currentUrl)}`;

  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-800 dark:via-gray-900 dark:to-indigo-900 rounded-2xl border border-blue-200 dark:border-gray-700 shadow-xl transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] my-8">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-indigo-500/5 to-purple-500/5"></div>
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-400/10 to-transparent rounded-full -translate-y-16 translate-x-16 animate-pulse"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-400/10 to-transparent rounded-full translate-y-12 -translate-x-12 animate-pulse delay-1000"></div>

      <div className="relative p-6 sm:p-8">
        {/* 头部图标和标题 */}
        <div className="flex items-center justify-center mb-6">
          <div className="relative group">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
              <Lock className="w-8 h-8 text-white transition-transform duration-300 group-hover:scale-110" />
            </div>
            <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-bounce">
              <Sparkles className="w-3 h-3 text-white" />
            </div>
          </div>
        </div>

        {/* 主标题 */}
        <div className="text-center mb-6">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            专属内容
          </h3>
          <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
            这篇精彩内容需要会员权限或单独解锁才能阅读
          </p>
        </div>

        {/* 内容区域 */}
        <div className="space-y-6">
          {!isAuthenticated ? (
            // 未登录用户
            <div className="text-center space-y-4">
              <p className="text-gray-700 dark:text-gray-300 mb-6">
                请登录您的账户以查看完整内容
              </p>
              
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link
                  href={loginUrlWithCallback}
                  className="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  <User className="w-4 h-4 mr-2" />
                  立即登录
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
                
                <Link
                  href={registerUrlWithCallback}
                  className="inline-flex items-center justify-center px-6 py-3 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 font-medium rounded-xl border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 shadow-md hover:shadow-lg"
                >
                  注册账户
                </Link>
              </div>
            </div>
          ) : step === 'selecting_payment' ? (
            // 步骤2: 选择支付方式
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-6 border border-white/50 dark:border-gray-700/50">
              <PaymentMethodSelector
                selectedMethod={selectedMethod}
                onSelect={(method) => {
                  setSelectedMethod(method);
                  setErrorMessage(null);
                }}
              />
              <button
                onClick={handleCreateOrderAndPay}
                disabled={!selectedMethod || isCreatingOrder}
                className="mt-4 w-full inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none disabled:cursor-not-allowed"
              >
                {isCreatingOrder ? (
                  <>
                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    正在创建订单...
                  </>
                ) : (
                  `确认支付 ¥${unlockPrice.toFixed(2)}`
                )}
              </button>
              {errorMessage && <p className="text-sm text-red-500 mt-2 text-center">{errorMessage}</p>}
            </div>
          ) : step === 'processing_payment' && orderId && selectedMethod ? (
            // 步骤3: 处理支付
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-6 border border-white/50 dark:border-gray-700/50">
              {selectedMethod === 'wallet' ? (
                <BalancePaymentForm
                  orderId={orderId}
                  title={`解锁文章: ${postTitle}`}
                  amount={unlockPrice}
                  onPaymentSuccess={handlePaymentSuccess}
                  onError={(err: any) => {
                    setErrorMessage(err.message || '支付时发生未知错误');
                    setStep('selecting_payment');
                  }}
                />
              ) : (
                <PaymentProcessor
                  orderId={orderId}
                  paymentMethod={selectedMethod}
                  onPaymentSuccess={handlePaymentSuccess}
                  onCancel={() => setStep('selecting_payment')}
                />
              )}
            </div>
          ) : step === 'success' ? (
            // 步骤4: 支付成功
             <div className="text-center p-6 bg-green-50 dark:bg-green-900/50 rounded-xl">
                <Sparkles className="w-12 h-12 text-green-500 mx-auto mb-4 animate-pulse" />
                <h4 className="font-semibold text-lg text-green-800 dark:text-green-200">解锁成功!</h4>
                <p className="text-green-700 dark:text-green-300 text-sm mt-2">
                  内容正在加载中，请稍候...
                </p>
             </div>
          ) : (
            // 步骤1: 初始状态，显示升级或解锁选项
            <>
              {/* 会员升级选项 */}
              {requiredMemberLevel > 0 && (
                <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-6 border border-white/50 dark:border-gray-700/50">
                  <div className="flex items-center mb-4">
                    <Crown className="w-5 h-5 text-yellow-500 mr-2" />
                    <h4 className="font-semibold text-gray-900 dark:text-white">
                      {getLevelDisplayInfo().name}专享
                    </h4>
                    {getLevelDisplayInfo().tier && (
                      <span className="ml-2 px-2 py-1 text-xs bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded-full">
                        {getLevelDisplayInfo().tier}
                      </span>
                    )}
                  </div>
                  
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                    {!isAuthenticated
                      ? `登录后即可查看${getLevelDisplayInfo().name}专享内容`
                      : !userMemberLevel
                        ? `开通${getLevelDisplayInfo().name}即可解锁此内容及更多专属文章`
                        : `升级到${getLevelDisplayInfo().name}即可解锁此内容及更多专属文章`
                    }
                  </p>

                  {!isAuthenticated ? (
                    <div className="flex gap-2">
                      <Link
                        href={loginUrl}
                        className="flex-1 inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                      >
                        <User className="w-4 h-4 mr-2" />
                        登录
                      </Link>
                      <Link
                        href={registerUrl}
                        className="flex-1 inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                      >
                        注册
                      </Link>
                    </div>
                  ) : (
                    <Link
                      href={upgradeUrlWithCallback}
                      className="inline-flex items-center justify-center w-full px-6 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    >
                      <Crown className="w-4 h-4 mr-2" />
                      {getMembershipButtonText()}
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Link>
                  )}
                </div>
              )}

              {/* 分隔线 */}
              {requiredMemberLevel > 0 && unlockPrice > 0 && (
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300 dark:border-gray-600"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-3 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-800 dark:via-gray-900 dark:to-indigo-900 text-gray-500 dark:text-gray-400">
                      或
                    </span>
                  </div>
                </div>
              )}

              {/* 单篇解锁选项 */}
              {unlockPrice > 0 && (
                <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-6 border border-white/50 dark:border-gray-700/50">
                  <div className="flex items-center mb-4">
                    <CreditCard className="w-5 h-5 text-green-500 mr-2" />
                    <h4 className="font-semibold text-gray-900 dark:text-white">
                      单篇解锁
                    </h4>
                  </div>
                  
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-gray-600 dark:text-gray-300 text-sm">
                      解锁这篇文章
                    </span>
                    <div className="text-right">
                      <span className="text-2xl font-bold text-green-600 dark:text-green-400">
                        ¥{unlockPrice.toFixed(2)}
                      </span>
                    </div>
                  </div>
                  
                  <button
                    onClick={handleUnlockClick}
                    className="inline-flex items-center justify-center w-full px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none disabled:cursor-not-allowed"
                  >
                    <CreditCard className="w-4 h-4 mr-2" />
                    立即解锁
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </button>
                  {errorMessage && <p className="text-sm text-red-500 mt-2 text-center">{errorMessage}</p>}
                </div>
              )}
            </>
          )}
        </div>

        {/* 底部提示 */}
        <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <p className="text-center text-xs text-gray-500 dark:text-gray-400">
            解锁后即可永久访问此内容
          </p>
        </div>
      </div>
    </div>
  );
};

export default PaywallCard;
