// @ts-nocheck
'use client';
import React, { useEffect, useState, useMemo } from 'react';
import { gql, useQuery } from '@apollo/client';
import { useAuthContext } from '@/hooks/useAuthContext';
import PaywallRenderer from './PaywallRenderer';

interface PaywallInfo {
  hasPaywall: boolean;
  previewContent: string;
  loginUrl: string;
  registerUrl: string;
  upgradeUrl: string;
  message: string;
  isLoggedIn: boolean;
}

interface PostContentSmartProps {
  postId: number; // WordPress databaseId
  initialContent: string;
  postTitle?: string;
  unlockPrice?: number;
  requiredMemberLevel?: number;
  isUnlocked?: boolean;
  paywallVariant?: 'default' | 'compact';
  graphqlSingleName?: string; // CPT的GraphQL单数名称，例如 'Post' 或 'Note'
  // 新增：预处理的付费墙信息
  initialPaywallInfo?: PaywallInfo | null;
}

// GraphQL 查询生成函数
const buildPostContentQuery = (graphqlSingleName: string) => {
  // 基础查询部分
  const baseQuery = `
    query GetContentNodeContent($id: ID!) {
      contentNode(id: $id, idType: DATABASE_ID) {
        ... on NodeWithContentEditor {
          content(format: RENDERED)
        }
        ... on NodeWithTitle {
          title
        }
  `;

  // 如果类型名有效，则添加特定类型的片段
  const typeFragment = graphqlSingleName
    ? `
        ... on ${graphqlSingleName} {
          unlockPrice
          requiredMemberLevel
          isUnlockedByCurrentUser
          paywallInfo {
            hasPaywall
            previewContent
            loginUrl
            registerUrl
            upgradeUrl
            message
            isLoggedIn
          }
        }
      `
    : '';

  // 闭合查询
  const closingBrace = `
      }
    }
  `;

  return gql`${baseQuery}${typeFragment}${closingBrace}`;
};

/**
 * 根据用户认证状态，决定是否重新拉取文章内容。
 * 如果初始内容包含付费墙标记，而用户已登录且有权限，再次请求以获得完整内容。
 * 使用精致的付费墙卡片替代原始HTML。
 */
const PostContentSmart: React.FC<PostContentSmartProps> = ({
  postId,
  initialContent,
  postTitle = '',
  unlockPrice = 0,
  requiredMemberLevel = 0,
  isUnlocked = false,
  paywallVariant = 'default',
  graphqlSingleName = 'Post', // 默认为 'Post' 以兼容旧用法
  initialPaywallInfo = null
}) => {
  const { isAuthenticated } = useAuthContext();

  // DEBUG-PAYWALL-INVESTIGATION: 组件初始化日志
  console.log(`[DEBUG-PostContentSmart] Component initialized for postId: ${postId}`, {
    initialContent: initialContent.substring(0, 100) + '...',
    postTitle,
    unlockPrice,
    requiredMemberLevel,
    isUnlocked,
    paywallVariant,
    graphqlSingleName,
    hasInitialPaywallInfo: !!initialPaywallInfo,
    isAuthenticated
  });

  const [content, setContent] = useState<string>(initialContent);
  const [postData, setPostData] = useState({
    title: postTitle,
    unlockPrice,
    requiredMemberLevel,
    isUnlocked
  });
  const [paywallInfo, setPaywallInfo] = useState<PaywallInfo | null>(initialPaywallInfo);

  // DEBUG: 状态变化监听
  useEffect(() => {
    console.log(`[DEBUG-PostContentSmart] Content state changed for postId: ${postId}`, {
      contentLength: content.length,
      hasPaywallMarker: content.includes('fd-member-access-denied'),
      contentPreview: content.substring(0, 200) + '...'
    });
  }, [content, postId]);

  useEffect(() => {
    console.log(`[DEBUG-PostContentSmart] PostData state changed for postId: ${postId}`, postData);
  }, [postData, postId]);

  useEffect(() => {
    console.log(`[DEBUG-PostContentSmart] PaywallInfo state changed for postId: ${postId}`, paywallInfo);
  }, [paywallInfo, postId]);

  // 当父组件传入的 props 发生变化时，同步更新本地 state
  useEffect(() => {
    console.log(`[DEBUG-PostContentSmart] Props changed, syncing to state for postId: ${postId}`, {
      oldContent: content.substring(0, 100) + '...',
      newContent: initialContent.substring(0, 100) + '...',
      oldPostData: postData,
      newPostData: {
        title: postTitle,
        unlockPrice,
        requiredMemberLevel,
        isUnlocked
      }
    });

    setContent(initialContent);
    setPostData({
      title: postTitle,
      unlockPrice,
      requiredMemberLevel,
      isUnlocked
    });
  }, [initialContent, postTitle, unlockPrice, requiredMemberLevel, isUnlocked]);

  // 检测初始内容是否包含付费墙提示 div
  const hasPaywall = initialContent.includes('fd-member-access-denied');

  // DEBUG: 付费墙检测日志
  console.log(`[DEBUG-PostContentSmart] Paywall detection for postId: ${postId}`, {
    hasPaywall,
    isAuthenticated,
    shouldSkipQuery: !isAuthenticated || !hasPaywall
  });

  // 使用 useMemo 动态构建查询，仅在 graphqlSingleName 变化时重新构建
  const POST_CONTENT_QUERY = useMemo(
    () => buildPostContentQuery(graphqlSingleName),
    [graphqlSingleName]
  );

  const { data, loading, refetch } = useQuery(POST_CONTENT_QUERY, {
    variables: { id: postId },
    skip: !isAuthenticated || !hasPaywall, // 仅在已登录且初始内容受限时再查询
    fetchPolicy: 'network-only',
  });

  // DEBUG: GraphQL查询状态日志
  useEffect(() => {
    console.log(`[DEBUG-PostContentSmart] GraphQL query state changed for postId: ${postId}`, {
      loading,
      hasData: !!data,
      dataKeys: data ? Object.keys(data) : [],
      skip: !isAuthenticated || !hasPaywall
    });
  }, [loading, data, postId, isAuthenticated, hasPaywall]);

  // 当从GraphQL获取到新的内容后，更新 state
  useEffect(() => {
    if (data?.contentNode) {
      const node = data.contentNode;

      console.log(`[DEBUG-PostContentSmart] Received new GraphQL data for postId: ${postId}`, {
        timestamp: new Date().toISOString(),
        nodeKeys: Object.keys(node),
        hasContent: !!node.content,
        contentLength: node.content?.length || 0,
        contentPreview: node.content?.substring(0, 200) + '...' || 'No content',
        hasPaywallMarker: node.content?.includes('fd-member-access-denied') || false,
        isUnlockedByCurrentUser: node.isUnlockedByCurrentUser,
        unlockPrice: node.unlockPrice,
        requiredMemberLevel: node.requiredMemberLevel,
        hasPaywallInfo: !!node.paywallInfo,
        paywallInfoDetails: node.paywallInfo
      });

      const oldContent = content;
      const oldPostData = postData;
      const oldPaywallInfo = paywallInfo;

      setContent(node.content ?? content);
      setPostData({
        title: node.title || postTitle,
        unlockPrice: node.unlockPrice ?? unlockPrice,
        requiredMemberLevel: node.requiredMemberLevel ?? requiredMemberLevel,
        isUnlocked: node.isUnlockedByCurrentUser ?? isUnlocked,
      });
      // 设置付费墙信息
      setPaywallInfo(node.paywallInfo);

      console.log(`[DEBUG-PostContentSmart] State updated after GraphQL data for postId: ${postId}`, {
        contentChanged: oldContent !== (node.content ?? content),
        postDataChanged: JSON.stringify(oldPostData) !== JSON.stringify({
          title: node.title || postTitle,
          unlockPrice: node.unlockPrice ?? unlockPrice,
          requiredMemberLevel: node.requiredMemberLevel ?? requiredMemberLevel,
          isUnlocked: node.isUnlockedByCurrentUser ?? isUnlocked,
        }),
        paywallInfoChanged: JSON.stringify(oldPaywallInfo) !== JSON.stringify(node.paywallInfo)
      });
    }
  }, [data, content, postTitle, unlockPrice, requiredMemberLevel, isUnlocked]);

  // 处理文章解锁成功后的回调
  const handleUnlockSuccess = () => {
    // 重新获取文章数据
    console.log(`[DEBUG-PostContentSmart] Unlock success callback triggered for postId: ${postId}`, {
      timestamp: new Date().toISOString(),
      currentLoading: loading,
      currentContent: content.substring(0, 100) + '...',
      currentPostData: postData,
      currentPaywallInfo: paywallInfo
    });

    console.log(`[DEBUG-PostContentSmart] Calling refetch() for postId: ${postId}...`);
    refetch().then((result) => {
      console.log(`[DEBUG-PostContentSmart] Refetch completed for postId: ${postId}`, {
        timestamp: new Date().toISOString(),
        hasData: !!result.data,
        dataKeys: result.data ? Object.keys(result.data) : [],
        loading: result.loading,
        error: result.error
      });
    }).catch((error) => {
      console.error(`[DEBUG-PostContentSmart] Refetch failed for postId: ${postId}`, error);
    });
  };

  if (loading) {
    console.log(`[DEBUG-PostContentSmart] Rendering loading state for postId: ${postId}`, {
      timestamp: new Date().toISOString(),
      loading,
      hasData: !!data
    });

    return (
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center space-x-2 text-gray-600">
          <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <span>加载中…</span>
        </div>
      </div>
    );
  }

  console.log(`[DEBUG-PostContentSmart] Rendering PaywallRenderer for postId: ${postId}`, {
    timestamp: new Date().toISOString(),
    contentLength: content.length,
    hasPaywallMarker: content.includes('fd-member-access-denied'),
    postData,
    hasPaywallInfo: !!paywallInfo,
    paywallVariant,
    loading
  });

  return (
    <PaywallRenderer
      content={content || ''}
      postId={postId}
      postTitle={postData.title}
      unlockPrice={postData.unlockPrice}
      requiredMemberLevel={postData.requiredMemberLevel}
      isUnlocked={postData.isUnlocked}
      variant={paywallVariant}
      onUnlock={handleUnlockSuccess}
      paywallInfo={paywallInfo}
      legacyMode={false}
      showLoading={false}
    />
  );
};

export default PostContentSmart; 