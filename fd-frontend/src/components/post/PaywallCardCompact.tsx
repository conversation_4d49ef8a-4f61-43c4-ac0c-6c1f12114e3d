'use client';

import React, { useState } from 'react';
import { Lock, Crown, CreditCard, User, ArrowRight, X } from 'lucide-react';
import { useAuthContext } from '@/hooks/useAuthContext';
import Link from 'next/link';
import { useMutation } from '@apollo/client';
import { CREATE_UNLOCK_ORDER } from '@/lib/graphql/mutations';
import PaymentMethodSelector from '../payment/PaymentMethodSelector';
import PaymentProcessor from '../payment/PaymentProcessor';
import BalancePaymentForm from '../payment/BalancePaymentForm';
import { MemberLevel } from '@/types/user-types';

type PaymentStep = 'initial' | 'selecting_payment' | 'creating_order' | 'processing_payment' | 'success';

interface PaywallCardCompactProps {
  postId: number;
  postTitle: string;
  requiredMemberLevel?: number;
  requiredMemberLevelInfo?: MemberLevel;
  unlockPrice?: number;
  isUnlocked?: boolean;
  userMemberLevel?: MemberLevel | null;
  loginUrl?: string;
  registerUrl?: string;
  upgradeUrl?: string;
  onUnlock?: () => void;
}

const PaywallCardCompact: React.FC<PaywallCardCompactProps> = ({
  postId,
  postTitle,
  requiredMemberLevel = 0,
  requiredMemberLevelInfo,
  unlockPrice = 0,
  isUnlocked = false,
  userMemberLevel,
  loginUrl = '/auth/login',
  registerUrl = '/auth/register',
  upgradeUrl = '/membership/upgrade',
  onUnlock,
}) => {
  const { isAuthenticated } = useAuthContext();
  const [step, setStep] = useState<PaymentStep>('initial');
  const [showModal, setShowModal] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const [createUnlockOrder, { loading: isCreatingOrder }] = useMutation(CREATE_UNLOCK_ORDER);

  // 判断是否应该显示"开通会员"还是"升级会员"
  const getMembershipButtonText = () => {
    if (!isAuthenticated) {
      return '登录查看';
    }

    if (!userMemberLevel) {
      return '开通会员';
    }

    return '升级会员';
  };

  // 获取等级显示信息
  const getLevelDisplayInfo = () => {
    if (requiredMemberLevelInfo) {
      return {
        name: requiredMemberLevelInfo.name,
        tier: requiredMemberLevelInfo.tier || '会员',
        description: requiredMemberLevelInfo.description || `${requiredMemberLevelInfo.name}专享内容`
      };
    }

    // 如果没有等级信息，使用默认显示
    return {
      name: '会员',
      tier: '会员',
      description: '会员专享内容'
    };
  };

  if (isUnlocked) {
    return null;
  }

  const resetToInitial = () => {
    setShowModal(false);
    setStep('initial');
    setErrorMessage(null);
    setSelectedMethod('');
  };

  const handleUnlockClick = () => {
    setErrorMessage(null);
    setStep('selecting_payment');
    setShowModal(true);
  };

  const handleCreateOrderAndPay = async () => {
    if (!selectedMethod) {
      setErrorMessage('请选择一种支付方式');
      return;
    }
    setErrorMessage(null);
    setStep('creating_order');

    try {
      const { data } = await createUnlockOrder({
        variables: {
          postId: String(postId),
          paymentMethod: selectedMethod,
          callbackUrl: typeof window !== 'undefined' ? window.location.href : ''
        },
      });

      if (data.createUnlockOrder.success && data.createUnlockOrder.orderId) {
        setOrderId(data.createUnlockOrder.orderId);
        setStep('processing_payment');
      } else {
        throw new Error(data.createUnlockOrder.message || '创建订单失败');
      }
    } catch (e: any) {
      console.error('创建解锁订单失败:', e);
      setErrorMessage(e.message || '发生未知错误，请稍后再试。');
      setStep('selecting_payment');
    }
  };

  const handlePaymentSuccess = () => {
    setStep('success');
    setTimeout(() => {
      resetToInitial();
      if (onUnlock) {
        console.log('[PaywallCardCompact] Payment success, calling onUnlock...');
        onUnlock();
    }
    }, 1500); // 显示成功信息1.5秒后关闭
  };

  const currentUrl = typeof window !== 'undefined' ? window.location.href : '';
  const loginUrlWithCallback = `${loginUrl}?callbackUrl=${encodeURIComponent(currentUrl)}`;
  const registerUrlWithCallback = `${registerUrl}?callbackUrl=${encodeURIComponent(currentUrl)}`;
  const upgradeUrlWithCallback = `${upgradeUrl}?returnUrl=${encodeURIComponent(currentUrl)}`;

  const renderModalContent = () => {
    switch (step) {
      case 'selecting_payment':
        return (
          <>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">选择支付方式</h3>
            <PaymentMethodSelector
              selectedMethod={selectedMethod}
              onSelect={(method) => {
                setSelectedMethod(method);
                setErrorMessage(null);
              }}
            />
            <button
              onClick={handleCreateOrderAndPay}
              disabled={!selectedMethod || isCreatingOrder}
              className="mt-4 w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium rounded-lg transition-all"
            >
              {isCreatingOrder ? '正在创建订单...' : `确认支付 ¥${unlockPrice.toFixed(2)}`}
            </button>
            {errorMessage && <p className="text-sm text-red-500 mt-2 text-center">{errorMessage}</p>}
          </>
        );
      case 'creating_order':
        return (
          <div className="flex items-center justify-center p-8">
             <div className="w-6 h-6 mr-2 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
             <span>正在安全地创建订单...</span>
          </div>
        );
      case 'processing_payment':
        return selectedMethod === 'wallet' ? (
          <BalancePaymentForm
            orderId={orderId!}
            title={`解锁文章: ${postTitle}`}
            amount={unlockPrice}
            onPaymentSuccess={handlePaymentSuccess}
            onError={(err: any) => {
              setErrorMessage(err.message || '支付失败');
              setStep('selecting_payment');
            }}
          />
        ) : (
          <PaymentProcessor
            orderId={orderId!}
            paymentMethod={selectedMethod}
            onPaymentSuccess={handlePaymentSuccess}
            onCancel={() => setStep('selecting_payment')}
          />
        );
      case 'success':
        return (
          <div className="text-center p-8">
            <h3 className="text-lg font-semibold text-green-600">解锁成功！</h3>
            <p className="text-gray-600 mt-2">内容即将刷新...</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <>
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 p-4 sm:p-6 my-8 hover:scale-[1.01]">
      <div className="flex items-start space-x-3 sm:space-x-4">
        <div className="flex-shrink-0">
          <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center transition-transform duration-300 hover:scale-110">
            <Lock className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </div>
        </div>
        <div className="flex-1 min-w-0">
            <div className="flex items-center mb-2">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {getLevelDisplayInfo().name}专享
              </h3>
              {getLevelDisplayInfo().tier && (
                <span className="ml-2 px-2 py-1 text-xs bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded-full">
                  {getLevelDisplayInfo().tier}
                </span>
              )}
            </div>
          {!isAuthenticated ? (
            <div>
                <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">请登录查看完整内容</p>
              <div className="flex flex-wrap gap-2">
                  <Link href={loginUrlWithCallback} className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                    <User className="w-4 h-4 mr-1" />登录
                </Link>
                  <Link href={registerUrlWithCallback} className="inline-flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm font-medium rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                  注册
                </Link>
              </div>
            </div>
          ) : (
              <div>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  {!userMemberLevel
                    ? `开通${getLevelDisplayInfo().name}或单篇解锁查看完整内容`
                    : `升级到${getLevelDisplayInfo().name}或单篇解锁查看完整内容`
                  }
                </p>
                <div className="flex flex-wrap gap-2 mt-3">
                {requiredMemberLevel > 0 && (
                    <Link href={upgradeUrlWithCallback} className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white text-sm font-medium rounded-lg transition-all">
                      <Crown className="w-4 h-4 mr-1" />{getMembershipButtonText()}
                  </Link>
                )}
                {unlockPrice > 0 && (
                    <button onClick={handleUnlockClick} className="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors">
                      <CreditCard className="w-4 h-4 mr-1" />解锁 ¥{unlockPrice.toFixed(2)}
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>

      {showModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50" aria-modal="true" role="dialog">
          <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md m-4">
             <button onClick={resetToInitial} className="absolute top-2 right-2 p-2 text-gray-500 hover:text-gray-800 dark:hover:text-gray-200 rounded-full transition-colors">
               <X className="w-5 h-5" />
             </button>
            <div className="p-6">
              {renderModalContent()}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default PaywallCardCompact;
