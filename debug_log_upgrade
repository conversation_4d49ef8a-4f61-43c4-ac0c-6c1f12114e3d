[DEBUG-PostContentSmart] Content state changed for postId: 25176 ObjectcontentLength: 2615contentPreview: "<p>&nbsp;</p>\r\n<p><strong>特朗普是病原体，还是零号病人？</strong></p>\r\n<hr />\r\n<p>编辑 | 从林</p>\r\n<p>编译 | 未来学人</p>\r\n<p>来源 | 经济学人<br /><br />好了吗？</p>\r\n<p>美国人担心特朗普会让美国沦为<strong>国际孤儿</strong>，但外国政府对特朗普式外交的看法却截然不同。他们更担心“美国..."hasPaywallMarker: false[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] PostData state changed for postId: 25176 ObjectisUnlocked: falserequiredMemberLevel: 3title: "《经济学人》| 美国优先，成了一种传染病"unlockPrice: 0.01[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] PaywallInfo state changed for postId: 25176 null
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Props changed, syncing to state for postId: 25176 Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] GraphQL query state changed for postId: 25176 ObjectdataKeys: []hasData: falseloading: falseskip: true[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Component initialized for postId: 25176 ObjectgraphqlSingleName: "Post"hasInitialPaywallInfo: falseinitialContent: "<p>&nbsp;</p>\r\n<p><strong>特朗普是病原体，还是零号病人？</strong></p>\r\n<hr />\r\n<p>编辑 | 从林</p>\r\n<p>编译 | 未来学人</p>\r\n<p..."isAuthenticated: trueisUnlocked: falsepaywallVariant: "default"postTitle: "《经济学人》| 美国优先，成了一种传染病"requiredMemberLevel: 3unlockPrice: 0.01[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Paywall detection for postId: 25176 ObjecthasPaywall: falseisAuthenticated: trueshouldSkipQuery: true[[Prototype]]: Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] Rendering PaywallRenderer for postId: 25176 ObjectcontentLength: 2615hasPaywallInfo: falsehasPaywallMarker: falseloading: falsepaywallVariant: "default"postData: {title: '《经济学人》| 美国优先，成了一种传染病', unlockPrice: 0.01, requiredMemberLevel: 3, isUnlocked: false}timestamp: "2025-08-01T14:49:31.100Z"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Component rendered for postId: 25176 ObjectcontentLength: 2615hasPaywallInfo: falsehasPaywallMarker: falseisUnlocked: falselegacyMode: falsepaywallInfoHasPaywall: undefinedrequiredMemberLevel: 3showLoading: falsetimestamp: "2025-08-01T14:49:31.100Z"unlockPrice: 0.01userMemberLevel: 3variant: "default"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Traditional mode detection for postId: 25176 ObjectcontentHasMarker: falsedisplayContentLength: 2615hasPaywall: falselegacyMode: falselevelLoading: truepaywallInfoHasPaywall: undefinedshowLoading: falsetimestamp: "2025-08-01T14:49:31.100Z"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] No paywall detected, rendering full content for postId: 25176 Object
8318-57878599d0bbdc6d.js:1 [DEBUG-PostContentSmart] PostData state changed for postId: 25176 ObjectisUnlocked: falserequiredMemberLevel: 3title: "《经济学人》| 美国优先，成了一种传染病"unlockPrice: 0.01[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Component rendered for postId: 25176 ObjectcontentLength: 2615hasPaywallInfo: falsehasPaywallMarker: falseisUnlocked: falselegacyMode: falsepaywallInfoHasPaywall: undefinedrequiredMemberLevel: 3showLoading: falsetimestamp: "2025-08-01T14:49:31.596Z"unlockPrice: 0.01userMemberLevel: 3variant: "default"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] Traditional mode detection for postId: 25176 ObjectcontentHasMarker: falsedisplayContentLength: 2615hasPaywall: falselegacyMode: falselevelLoading: falsepaywallInfoHasPaywall: undefinedshowLoading: falsetimestamp: "2025-08-01T14:49:31.597Z"[[Prototype]]: Object
213-bbe728ad8fefd65d.js:1 [DEBUG-PaywallRenderer] No paywall detected, rendering full content for postId: 25176 ObjectcontentLength: 2615isUnlocked: falsetimestamp: "2025-08-01T14:49:31.597Z"[[Prototype]]: Object



 [01-Aug-2025 14:48:51 UTC] [DEBUG] Checking access for post_id: 25176, user_id: 117
[01-Aug-2025 14:48:51 UTC] [DEBUG] Post 25176 requires level_id: 3
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:48:51 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: ""
[01-Aug-2025 14:48:51 UTC] [DEBUG] Final access check for post_id: 25176, user_id: 117. User priority: -1, Required priority: 50. Access: Denied
[01-Aug-2025 14:48:51 UTC] ==== 相关文章设置调试 ====
[01-Aug-2025 14:48:51 UTC] 文章ID: 25176
[01-Aug-2025 14:48:51 UTC] 文章标题: 《经济学人》| 美国优先，成了一种传染病
[01-Aug-2025 14:48:51 UTC] fd_related_posts_count选项值: 12
[01-Aug-2025 14:48:51 UTC] GraphQL参数count值: 未设置
[01-Aug-2025 14:48:51 UTC] 最终使用的count值: 12
[01-Aug-2025 14:48:51 UTC] fd_module_settings选项值: Array
(
    [fd_related_posts_strategy] => mixed
    [fd_related_posts_count] => 12
    [fd_posts_per_page] => 12
)

[01-Aug-2025 14:48:51 UTC] ==========================
[01-Aug-2025 14:48:51 UTC] GraphQL relatedPosts使用策略: mixed, 数量: 12
[01-Aug-2025 14:48:51 UTC] [DEBUG] Checking access for post_id: 25176, user_id: 117
[01-Aug-2025 14:48:51 UTC] [DEBUG] Post 25176 requires level_id: 3
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:48:51 UTC] PHP Deprecated:  函数 WPGraphQL\Data\DataSource::resolve_post_object 自版本 0.8.4 起已<strong>弃用</strong>！请使用 Use $context->get_loader( 'post' )->load_deferred( $id ) instead. 代替。 in /var/www/html/wp-includes/functions.php on line 6121
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:48:51 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: ""
[01-Aug-2025 14:48:51 UTC] [DEBUG] Final access check for post_id: 25176, user_id: 117. User priority: -1, Required priority: 50. Access: Denied
[01-Aug-2025 14:48:51 UTC] [DEBUG-GraphQL-IsUnlocked] Checking unlock status for post_id: 25176, user_id: 117
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:48:51 UTC] [DEBUG-GraphQL-IsUnlocked] Unlock status for post_id: 25176, user_id: 117: LOCKED
[01-Aug-2025 14:48:51 UTC] [DEBUG] Checking access for post_id: 25176, user_id: 117
[01-Aug-2025 14:48:51 UTC] [DEBUG] Post 25176 requires level_id: 3
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:48:51 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: ""
[01-Aug-2025 14:48:51 UTC] [DEBUG] Final access check for post_id: 25176, user_id: 117. User priority: -1, Required priority: 50. Access: Denied
[01-Aug-2025 14:48:51 UTC] ==== 相关文章设置调试 ====
[01-Aug-2025 14:48:51 UTC] 文章ID: 25176
[01-Aug-2025 14:48:51 UTC] 文章标题: 《经济学人》| 美国优先，成了一种传染病
[01-Aug-2025 14:48:51 UTC] fd_related_posts_count选项值: 12
[01-Aug-2025 14:48:51 UTC] GraphQL参数count值: 未设置
[01-Aug-2025 14:48:51 UTC] 最终使用的count值: 12
[01-Aug-2025 14:48:51 UTC] fd_module_settings选项值: Array
(
    [fd_related_posts_strategy] => mixed
    [fd_related_posts_count] => 12
    [fd_posts_per_page] => 12
)

[01-Aug-2025 14:48:51 UTC] ==========================
[01-Aug-2025 14:48:51 UTC] GraphQL relatedPosts使用策略: mixed, 数量: 12
[01-Aug-2025 14:48:51 UTC] PHP Deprecated:  函数 WPGraphQL\Data\DataSource::resolve_post_object 自版本 0.8.4 起已<strong>弃用</strong>！请使用 Use $context->get_loader( 'post' )->load_deferred( $id ) instead. 代替。 in /var/www/html/wp-includes/functions.php on line 6121
[01-Aug-2025 14:48:51 UTC] [DEBUG-GraphQL-IsUnlocked] Checking unlock status for post_id: 25176, user_id: 117
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:48:51 UTC] [DEBUG-GraphQL-IsUnlocked] Unlock status for post_id: 25176, user_id: 117: LOCKED
[01-Aug-2025 14:48:51 UTC] [DEBUG] Checking access for post_id: 25176, user_id: 117
[01-Aug-2025 14:48:51 UTC] [DEBUG] Post 25176 requires level_id: 3
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:48:51 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:48:51 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: ""
[01-Aug-2025 14:48:51 UTC] [DEBUG] Final access check for post_id: 25176, user_id: 117. User priority: -1, Required priority: 50. Access: Denied
[01-Aug-2025 14:48:54 UTC] [DEBUG] Checking access for post_id: 25176, user_id: 117
[01-Aug-2025 14:48:54 UTC] [DEBUG] Post 25176 requires level_id: 3
[01-Aug-2025 14:48:54 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:48:54 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:48:54 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:48:54 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: ""
[01-Aug-2025 14:48:54 UTC] [DEBUG] Final access check for post_id: 25176, user_id: 117. User priority: -1, Required priority: 50. Access: Denied
[01-Aug-2025 14:48:54 UTC] [DEBUG-GraphQL-IsUnlocked] Checking unlock status for post_id: 25176, user_id: 117
[01-Aug-2025 14:48:54 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:48:54 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:48:54 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:48:54 UTC] [DEBUG-GraphQL-IsUnlocked] Unlock status for post_id: 25176, user_id: 117: LOCKED
[01-Aug-2025 14:48:54 UTC] [DEBUG] Checking access for post_id: 25176, user_id: 117
[01-Aug-2025 14:48:54 UTC] [DEBUG] Post 25176 requires level_id: 3
[01-Aug-2025 14:48:54 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:48:54 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:48:54 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:48:54 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: ""
[01-Aug-2025 14:48:54 UTC] [DEBUG] Final access check for post_id: 25176, user_id: 117. User priority: -1, Required priority: 50. Access: Denied
[01-Aug-2025 14:49:23 UTC] [FD WebSocket Push DEBUG] Processing payment success event
[01-Aug-2025 14:49:23 UTC] [FD WebSocket Push DEBUG] Payment is not a post unlock order, skipping
[01-Aug-2025 14:49:24 UTC] [DEBUG-Payment-Complete] Triggering fd_payment_order_completed action for order_id: 178, payment_method: wallet
[01-Aug-2025 14:49:24 UTC] [DEBUG-Payment-Handler] ========== POST UNLOCK PAYMENT HANDLER START ==========
[01-Aug-2025 14:49:24 UTC] [DEBUG-Payment-Handler] Timestamp: 2025-08-01 22:49:24
[01-Aug-2025 14:49:24 UTC] [DEBUG-Payment-Handler] Order ID: 178
[01-Aug-2025 14:49:24 UTC] [DEBUG-Payment-Handler] fd_member_handle_post_unlock_payment triggered for order_id: 178
[01-Aug-2025 14:49:24 UTC] [DEBUG-Payment-Handler] Order found: {"id":"178","user_id":"117","payment_status":"paid","amount":"0.02","product_type":"member_level","product_id":"3"}
[01-Aug-2025 14:49:24 UTC] [DEBUG-Payment-Handler] Raw metadata: {"from_level_id":"none","to_level_id":3,"from_priority":0,"to_priority":50,"returnUrl":"https://www.futuredecade.com/article/250414-436182/america-first-test"}
[01-Aug-2025 14:49:24 UTC] [DEBUG-Payment-Handler] Parsed metadata: {"from_level_id":"none","to_level_id":3,"from_priority":0,"to_priority":50,"returnUrl":"https:\/\/www.futuredecade.com\/article\/250414-436182\/america-first-test"}
[01-Aug-2025 14:49:24 UTC] [DEBUG-Payment-Handler] Order 178 is not a post_unlock order. Meta: {"from_level_id":"none","to_level_id":3,"from_priority":0,"to_priority":50,"returnUrl":"https:\/\/www.futuredecade.com\/article\/250414-436182\/america-first-test"}
[01-Aug-2025 14:49:24 UTC] [DEBUG-Payment-Complete] fd_payment_order_completed action completed for order_id: 178
[01-Aug-2025 14:49:31 UTC] [DEBUG] Checking access for post_id: 25176, user_id: 117
[01-Aug-2025 14:49:31 UTC] [DEBUG] Post 25176 requires level_id: 3
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:49:31 UTC] [DEBUG] Cache MISS for user_id: 117 member level.
[01-Aug-2025 14:49:31 UTC] [DEBUG] Final access check for post_id: 25176, user_id: 117. User priority: 50, Required priority: 50. Access: Granted
[01-Aug-2025 14:49:31 UTC] ==== 相关文章设置调试 ====
[01-Aug-2025 14:49:31 UTC] 文章ID: 25176
[01-Aug-2025 14:49:31 UTC] 文章标题: 《经济学人》| 美国优先，成了一种传染病
[01-Aug-2025 14:49:31 UTC] fd_related_posts_count选项值: 12
[01-Aug-2025 14:49:31 UTC] GraphQL参数count值: 未设置
[01-Aug-2025 14:49:31 UTC] 最终使用的count值: 12
[01-Aug-2025 14:49:31 UTC] fd_module_settings选项值: Array
(
    [fd_related_posts_strategy] => mixed
    [fd_related_posts_count] => 12
    [fd_posts_per_page] => 12
)

[01-Aug-2025 14:49:31 UTC] ==========================
[01-Aug-2025 14:49:31 UTC] GraphQL relatedPosts使用策略: mixed, 数量: 12
[01-Aug-2025 14:49:31 UTC] PHP Deprecated:  函数 WPGraphQL\Data\DataSource::resolve_post_object 自版本 0.8.4 起已<strong>弃用</strong>！请使用 Use $context->get_loader( 'post' )->load_deferred( $id ) instead. 代替。 in /var/www/html/wp-includes/functions.php on line 6121
[01-Aug-2025 14:49:31 UTC] [DEBUG-GraphQL-IsUnlocked] Checking unlock status for post_id: 25176, user_id: 117
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:49:31 UTC] [DEBUG-GraphQL-IsUnlocked] Unlock status for post_id: 25176, user_id: 117: LOCKED
[01-Aug-2025 14:49:31 UTC] [DEBUG] Checking access for post_id: 25176, user_id: 117
[01-Aug-2025 14:49:31 UTC] [DEBUG] Post 25176 requires level_id: 3
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:49:31 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: {"id":3,"name":"\u5b63\u4ed8\u4f1a\u5458","description":"\u5b63\u5ea6\u4ed8\u8d39","priority":50,"price":0.02,"duration":3,"duration_unit":"months"}
[01-Aug-2025 14:49:31 UTC] [DEBUG] Final access check for post_id: 25176, user_id: 117. User priority: 50, Required priority: 50. Access: Granted
[01-Aug-2025 14:49:31 UTC] [DEBUG] Checking access for post_id: 25176, user_id: 117
[01-Aug-2025 14:49:31 UTC] [DEBUG] Post 25176 requires level_id: 3
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:49:31 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: {"id":3,"name":"\u5b63\u4ed8\u4f1a\u5458","description":"\u5b63\u5ea6\u4ed8\u8d39","priority":50,"price":0.02,"duration":3,"duration_unit":"months"}
[01-Aug-2025 14:49:31 UTC] [DEBUG] Final access check for post_id: 25176, user_id: 117. User priority: 50, Required priority: 50. Access: Granted
[01-Aug-2025 14:49:31 UTC] ==== 相关文章设置调试 ====
[01-Aug-2025 14:49:31 UTC] 文章ID: 25176
[01-Aug-2025 14:49:31 UTC] 文章标题: 《经济学人》| 美国优先，成了一种传染病
[01-Aug-2025 14:49:31 UTC] fd_related_posts_count选项值: 12
[01-Aug-2025 14:49:31 UTC] GraphQL参数count值: 未设置
[01-Aug-2025 14:49:31 UTC] 最终使用的count值: 12
[01-Aug-2025 14:49:31 UTC] fd_module_settings选项值: Array
(
    [fd_related_posts_strategy] => mixed
    [fd_related_posts_count] => 12
    [fd_posts_per_page] => 12
)

[01-Aug-2025 14:49:31 UTC] ==========================
[01-Aug-2025 14:49:31 UTC] GraphQL relatedPosts使用策略: mixed, 数量: 12
[01-Aug-2025 14:49:31 UTC] PHP Deprecated:  函数 WPGraphQL\Data\DataSource::resolve_post_object 自版本 0.8.4 起已<strong>弃用</strong>！请使用 Use $context->get_loader( 'post' )->load_deferred( $id ) instead. 代替。 in /var/www/html/wp-includes/functions.php on line 6121
[01-Aug-2025 14:49:31 UTC] [DEBUG-GraphQL-IsUnlocked] Checking unlock status for post_id: 25176, user_id: 117
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:49:31 UTC] [DEBUG-GraphQL-IsUnlocked] Unlock status for post_id: 25176, user_id: 117: LOCKED
[01-Aug-2025 14:49:31 UTC] [DEBUG] Checking access for post_id: 25176, user_id: 117
[01-Aug-2025 14:49:31 UTC] [DEBUG] Post 25176 requires level_id: 3
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25176
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25176
[01-Aug-2025 14:49:31 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 14:49:31 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: {"id":3,"name":"\u5b63\u4ed8\u4f1a\u5458","description":"\u5b63\u5ea6\u4ed8\u8d39","priority":50,"price":0.02,"duration":3,"duration_unit":"months"}
[01-Aug-2025 14:49:31 UTC] [DEBUG] Final access check for post_id: 25176, user_id: 117. User priority: 50, Required priority: 50. Access: Granted
                    

