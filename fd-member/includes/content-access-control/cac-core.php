<?php
/**
 * Core logic for Post Content Access Control.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

define('FD_POST_ACCESS_LEVEL_META_KEY', '_fd_required_member_level');
define('FD_POST_UNLOCK_PRICE_META_KEY', '_fd_unlock_price');

/**
 * The core permission check function.
 * Determines if the current user can view a specific post.
 *
 * @param int $post_id The ID of the post to check.
 * @return bool True if the user has access, false otherwise.
 */
function fd_member_user_can_view_post($post_id = null) {
    if (is_null($post_id)) {
        $post_id = get_the_ID();
    }

    $user_id_for_log = get_current_user_id();
    error_log("[DEBUG] Checking access for post_id: {$post_id}, user_id: {$user_id_for_log}");
    
    // Allow access for certain query types, like admin or feeds.
    if (is_admin() || is_feed()) {
        error_log("[DEBUG] Access granted for post_id: {$post_id} because it's admin or feed.");
        return true;
    }

    // If the current user can edit this post, always allow (author/administrator preview).
    if (current_user_can('edit_post', $post_id)) {
        error_log("[DEBUG] Access granted for post_id: {$post_id} because user can edit.");
        return true;
    }

    // If the post type is not in the restricted list, always allow.
    if (!in_array(get_post_type($post_id), fd_member_get_restricted_post_types(), true)) {
        error_log("[DEBUG] Access granted for post_id: {$post_id} because post type is not restricted.");
        return true;
    }

    // Respect WordPress password-protected posts – rely on core flow.
    if (post_password_required($post_id)) {
        error_log("[DEBUG] Access check deferred for post_id: {$post_id} due to password protection.");
        return true; // Core will handle password prompt.
    }

    $required_level_id = intval(get_post_meta($post_id, FD_POST_ACCESS_LEVEL_META_KEY, true));
    error_log("[DEBUG] Post {$post_id} requires level_id: {$required_level_id}");

    // 0 is public
    if ($required_level_id === 0) {
        error_log("[DEBUG] Access granted for post_id: {$post_id} because it's public.");
        return true;
    }
    
    $user_id = get_current_user_id();

    // -1 is for any logged-in user
    if ($required_level_id === -1) {
        $has_access = $user_id > 0;
        error_log("[DEBUG] Access for post_id: {$post_id} (requires logged in user): " . ($has_access ? 'Granted' : 'Denied'));
        return $has_access;
    }

    // 检查用户是否已单独解锁该文章
    if ($user_id && function_exists('fd_member_has_user_unlocked_post') && fd_member_has_user_unlocked_post($user_id, $post_id)) {
        error_log("[DEBUG] Access granted for post_id: {$post_id}, user_id: {$user_id} due to individual unlock.");
        return true;
    }

    // If a specific level is required, but user is not logged in, deny access.
    if ($required_level_id > 0 && !$user_id) {
        error_log("[DEBUG] Access denied for post_id: {$post_id} because user is not logged in.");
        return false;
    }

    // Get user's level priority
    $user_level_info = fd_member_get_user_member_level_cached($user_id);
    $user_priority = $user_level_info ? intval($user_level_info['priority']) : -1;
    
    // Get required level priority
    $required_level_info = fd_member_get_member_level($required_level_id);
    $required_priority = $required_level_info ? intval($required_level_info['priority']) : 0;

    $final_access = $user_priority >= $required_priority;
    error_log("[DEBUG] Final access check for post_id: {$post_id}, user_id: {$user_id}. User priority: {$user_priority}, Required priority: {$required_priority}. Access: " . ($final_access ? 'Granted' : 'Denied'));
    
    return $final_access;
}

/**
 * Cached wrapper for fd_member_get_user_member_level to reduce DB hits.
 */
function fd_member_get_user_member_level_cached($user_id) {
    $cache_key = 'member_level_' . $user_id;
    $level     = wp_cache_get($cache_key, 'fd_member_levels');
    if ($level === false) {
        error_log("[DEBUG] Cache MISS for user_id: {$user_id} member level.");
        $level = fd_member_get_user_member_level($user_id);
        wp_cache_set($cache_key, $level, 'fd_member_levels', 300); // cache 5min
    } else {
        error_log("[DEBUG] Cache HIT for user_id: {$user_id} member level. Level: " . json_encode($level));
    }
    return $level;
}

/**
 * Get the list of post types that should be protected by member access control.
 * By default, include all public post types with UI, excluding attachments, revisions, and menu items.
 * Developers can further modify via the 'fd_member_restricted_post_types' filter.
 *
 * @return array
 */
function fd_member_get_restricted_post_types(): array {
    $types = get_post_types(
        [
            'public'  => true,
            'show_ui' => true,
        ],
        'names'
    );

    // Exclude types that never need protection.
    $exclude = ['attachment', 'revision', 'nav_menu_item'];
    $types   = array_diff($types, $exclude);

    return apply_filters('fd_member_restricted_post_types', $types);
}

/**
 * Filter the content of the post based on user's access rights.
 *
 * @param string $content The original post content.
 * @return string The modified content (or original if access is granted).
 */
function fd_member_filter_the_content($content) {
    // Do not filter in feeds, excerpts, or admin screens.
    if (is_feed() || is_admin() || !is_singular(fd_member_get_restricted_post_types())) {
        return $content;
    }

    $post_id = get_the_ID();

    if (fd_member_user_can_view_post($post_id)) {
        return $content;
    } else {
        // User does not have access. Send private cache headers to avoid CDN leakage.
        if (!headers_sent()) {
            header('Cache-Control: private, no-store, no-cache, must-revalidate');
            header('Vary: Cookie');
        }
        // User does not have access.
        $message = '<div class="fd-member-access-denied">';
        $message .= '<h3>' . __('Members Only Content', 'fd-member') . '</h3>';
        
        $login_link    = get_option('fd_member_login_url');
        $register_link = get_option('fd_member_register_url');
        $upgrade_link  = get_option('fd_member_upgrade_url');

        // 回退到旧逻辑
        if (!$login_link)    { $login_link    = wp_login_url(get_permalink()); }
        if (!$register_link) { $register_link = wp_registration_url(); }
        if (!$upgrade_link)  { $upgrade_link  = home_url('/membership/upgrade'); }

        if (is_user_logged_in()) {
            $message .= '<p>' . __('This content is reserved for a higher membership level.', 'fd-member') . '</p>';
            $message .= '<a href="' . esc_url($upgrade_link) . '" class="button">' . __('Upgrade Your Membership', 'fd-member') . '</a>';
        } else {
            $message .= '<p>' . __('Please log in or register to view this content.', 'fd-member') . '</p>';
            $message .= '<a href="' . esc_url($login_link) . '" class="button">' . __('Log In', 'fd-member') . '</a> ';
            $message .= '<a href="' . esc_url($register_link) . '" class="button">' . __('Register', 'fd-member') . '</a>';
        }
        
        $message .= '</div>';
        
        $preview_html = fd_member_generate_preview($post_id);
        return $preview_html . $message;
    }
}
add_filter('the_content', 'fd_member_filter_the_content', 10);

/**
 * Generate preview content based on preview mode.
 */
function fd_member_generate_preview($post_id): string {
    $mode  = get_post_meta($post_id, '_fd_preview_mode', true) ?: 'excerpt';
    $value = get_post_meta($post_id, '_fd_preview_value', true);

    switch ($mode) {
        case 'first_chars':
            $length  = intval($value) ?: 120;
            $text    = wp_strip_all_tags(get_post_field('post_content', $post_id));
            $preview = mb_substr($text, 0, $length) . '…';
            return '<p>' . esc_html($preview) . '</p>';
        case 'custom':
            return wp_kses_post($value);
        case 'excerpt':
        default:
            return has_excerpt($post_id) ? get_the_excerpt($post_id) : '';
    }
}

/**
 * 处理支付成功后的文章解锁
 * 
 * @param int $order_id 订单ID
 */
function fd_member_handle_post_unlock_payment($order_id) {
    $timestamp = current_time('mysql');
    error_log("[DEBUG-Payment-Handler] ========== POST UNLOCK PAYMENT HANDLER START ==========");
    error_log("[DEBUG-Payment-Handler] Timestamp: {$timestamp}");
    error_log("[DEBUG-Payment-Handler] Order ID: {$order_id}");

    // 检查订单是否存在
    if (!function_exists('fd_payment_get_order')) {
        error_log("[ERROR-Payment-Handler] fd_payment_get_order function does not exist.");
        return;
    }

    error_log("[DEBUG-Payment-Handler] fd_member_handle_post_unlock_payment triggered for order_id: {$order_id}");
    $order = fd_payment_get_order($order_id);
    if (!$order) {
        error_log("[ERROR-Payment-Handler] Order not found for order_id: {$order_id}");
        return;
    }

    error_log("[DEBUG-Payment-Handler] Order found: " . json_encode([
        'id' => $order->id,
        'user_id' => $order->user_id,
        'payment_status' => $order->payment_status,
        'amount' => $order->amount,
        'product_type' => $order->product_type ?? 'N/A',
        'product_id' => $order->product_id ?? 'N/A'
    ]));

    // 检查订单是否已支付，使用 payment_status 字段和 'paid' 状态
    if ($order->payment_status !== 'paid') {
        error_log("[DEBUG-Payment-Handler] Order {$order_id} status is '{$order->payment_status}', not 'paid'. Skipping unlock.");
        return;
    }
    
    // 检查订单类型是否为文章解锁 (兼容 JSON 或 serialized 数组)
    $meta_raw = $order->metadata ?? $order->meta ?? '';
    error_log("[DEBUG-Payment-Handler] Raw metadata: " . substr($meta_raw, 0, 500));

    $meta = is_string($meta_raw) ? json_decode($meta_raw, true) : $meta_raw;
    if (!is_array($meta)) {
        $meta = maybe_unserialize($meta_raw);
    }

    error_log("[DEBUG-Payment-Handler] Parsed metadata: " . json_encode($meta));

    if (!is_array($meta) || empty($meta['order_type']) || $meta['order_type'] !== 'post_unlock' || empty($meta['post_id'])) {
        error_log("[DEBUG-Payment-Handler] Order {$order_id} is not a post_unlock order. Meta: " . json_encode($meta));
        return;
    }

    $user_id = $order->user_id;
    $post_id = absint($meta['post_id']);

    error_log("[DEBUG-Payment-Handler] Processing unlock for post_id: {$post_id}, user_id: {$user_id}, order_id: {$order_id}");

    // 记录用户解锁文章
    if (function_exists('fd_member_record_post_unlock')) {
        error_log("[DEBUG-Payment-Handler] Calling fd_member_record_post_unlock...");
        $result = fd_member_record_post_unlock($user_id, $post_id);
        error_log("[DEBUG-Payment-Handler] fd_member_record_post_unlock result for user {$user_id}, post {$post_id}: " . ($result ? 'SUCCESS' : 'FAILURE'));

        if ($result) {
            error_log("[DEBUG-Payment-Handler] Post unlock completed successfully!");
        } else {
            error_log("[ERROR-Payment-Handler] Failed to record post unlock!");
        }
    } else {
        error_log("[ERROR-Payment-Handler] fd_member_record_post_unlock function does not exist!");
    }

    error_log("[DEBUG-Payment-Handler] ========== POST UNLOCK PAYMENT HANDLER END ==========");
}
add_action('fd_payment_order_completed', 'fd_member_handle_post_unlock_payment'); 