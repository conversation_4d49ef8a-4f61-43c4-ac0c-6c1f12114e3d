<?php
/**
 * Database operations for Content Access Control.
 * Handles the creation and management of the unlocked posts table.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Create the unlocked posts database table.
 * This function is called during plugin activation.
 */
function fd_member_create_unlocked_posts_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'unlocked_posts';
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        post_id bigint(20) NOT NULL,
        unlocked_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY  (id),
        KEY user_id (user_id),
        KEY post_id (post_id),
        UNIQUE KEY user_post (user_id, post_id)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Check if a user has unlocked a specific post.
 *
 * @param int $user_id The user ID.
 * @param int $post_id The post ID.
 * @return bool True if the user has unlocked the post, false otherwise.
 */
function fd_member_has_user_unlocked_post($user_id, $post_id) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'unlocked_posts';
    error_log("[DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: {$user_id}, post_id: {$post_id}");
    error_log("[DEBUG-DB-Check] Query: SELECT COUNT(*) FROM {$table_name} WHERE user_id = {$user_id} AND post_id = {$post_id}");

    $result = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name WHERE user_id = %d AND post_id = %d",
        $user_id,
        $post_id
    ));

    error_log("[DEBUG-DB-Check] Query result: {$result}, Status: " . ($result > 0 ? 'UNLOCKED' : 'LOCKED'));
    return $result > 0;
}

/**
 * Record that a user has unlocked a post.
 *
 * @param int $user_id The user ID.
 * @param int $post_id The post ID.
 * @return bool True on success, false on failure.
 */
function fd_member_record_post_unlock($user_id, $post_id) {
    global $wpdb;

    $timestamp = current_time('mysql');
    error_log("[DEBUG-DB-Write] fd_member_record_post_unlock called for user_id: {$user_id}, post_id: {$post_id} at {$timestamp}");

    // 检查是否已解锁，避免重复记录
    if (fd_member_has_user_unlocked_post($user_id, $post_id)) {
        error_log("[DEBUG-DB-Write] User has already unlocked this post. No action needed.");
        return true;
    }

    $table_name = $wpdb->prefix . 'unlocked_posts';
    error_log("[DEBUG-DB-Write] Inserting unlock record into {$table_name}");

    $result = $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'post_id' => $post_id,
            'unlocked_at' => $timestamp
        ),
        array('%d', '%d', '%s')
    );

    if ($result !== false) {
        $insert_id = $wpdb->insert_id;
        error_log("[DEBUG-DB-Write] Successfully inserted unlock record with ID: {$insert_id} for user_id: {$user_id}, post_id: {$post_id}");

        // 验证插入是否成功
        $verify_result = fd_member_has_user_unlocked_post($user_id, $post_id);
        error_log("[DEBUG-DB-Write] Verification check after insert: " . ($verify_result ? 'CONFIRMED' : 'FAILED'));
    } else {
        error_log("[DEBUG-DB-Write] Failed to insert unlock record for user_id: {$user_id}, post_id: {$post_id}");
        error_log("[DEBUG-DB-Write] Database error: " . $wpdb->last_error);
    }

    return $result !== false;
}

/**
 * Get all posts unlocked by a specific user.
 *
 * @param int $user_id The user ID.
 * @return array Array of post IDs.
 */
function fd_member_get_user_unlocked_posts($user_id) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'unlocked_posts';
    $results = $wpdb->get_col($wpdb->prepare(
        "SELECT post_id FROM $table_name WHERE user_id = %d",
        $user_id
    ));
    
    return array_map('intval', $results);
}

/**
 * Get all users who have unlocked a specific post.
 *
 * @param int $post_id The post ID.
 * @return array Array of user IDs.
 */
function fd_member_get_post_unlocked_users($post_id) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'unlocked_posts';
    $results = $wpdb->get_col($wpdb->prepare(
        "SELECT user_id FROM $table_name WHERE post_id = %d",
        $post_id
    ));
    
    return array_map('intval', $results);
} 