#!/bin/bash

# DEBUG-PAYWALL-INVESTIGATION: 付费墙调试日志监控脚本
# 用于实时监控付费墙解锁过程中的前后端日志

echo "=========================================="
echo "付费墙解锁调试日志监控"
echo "=========================================="
echo "监控以下调试标记的日志："
echo "- [DEBUG-PostContentSmart]"
echo "- [DEBUG-PaywallRenderer]"
echo "- [DEBUG-PaywallCard]"
echo "- [DEBUG-PaymentProcessor]"
echo "- [DEBUG-GraphQL-*]"
echo "- [DEBUG-DB-*]"
echo "- [DEBUG-Payment-*]"
echo "=========================================="
echo ""

# 获取WordPress调试日志路径
WP_DEBUG_LOG="/var/log/nginx/error.log"
if [ -f "wp-content/debug.log" ]; then
    WP_DEBUG_LOG="wp-content/debug.log"
elif [ -f "/tmp/wordpress-debug.log" ]; then
    WP_DEBUG_LOG="/tmp/wordpress-debug.log"
fi

echo "监控日志文件: $WP_DEBUG_LOG"
echo "按 Ctrl+C 停止监控"
echo ""

# 实时监控日志，过滤调试信息
tail -f "$WP_DEBUG_LOG" 2>/dev/null | grep -E "\[DEBUG-(PostContentSmart|PaywallRenderer|PaywallCard|PaymentProcessor|GraphQL|DB|Payment)" --line-buffered | while read line; do
    # 添加时间戳和颜色
    timestamp=$(date '+%H:%M:%S')
    
    # 根据不同的调试类型使用不同颜色
    if [[ $line == *"[DEBUG-PostContentSmart]"* ]]; then
        echo -e "\033[32m[$timestamp]\033[0m \033[36m$line\033[0m"  # 青色
    elif [[ $line == *"[DEBUG-PaywallRenderer]"* ]]; then
        echo -e "\033[32m[$timestamp]\033[0m \033[35m$line\033[0m"  # 紫色
    elif [[ $line == *"[DEBUG-PaywallCard]"* ]]; then
        echo -e "\033[32m[$timestamp]\033[0m \033[33m$line\033[0m"  # 黄色
    elif [[ $line == *"[DEBUG-PaymentProcessor]"* ]]; then
        echo -e "\033[32m[$timestamp]\033[0m \033[34m$line\033[0m"  # 蓝色
    elif [[ $line == *"[DEBUG-GraphQL"* ]]; then
        echo -e "\033[32m[$timestamp]\033[0m \033[31m$line\033[0m"  # 红色
    elif [[ $line == *"[DEBUG-DB"* ]]; then
        echo -e "\033[32m[$timestamp]\033[0m \033[37m$line\033[0m"  # 白色
    elif [[ $line == *"[DEBUG-Payment"* ]]; then
        echo -e "\033[32m[$timestamp]\033[0m \033[93m$line\033[0m"  # 亮黄色
    else
        echo -e "\033[32m[$timestamp]\033[0m $line"
    fi
done
