# 付费墙解锁问题调试指南

## 问题描述
付费墙组件在会员升级到解锁需要的等级、单篇解锁支付成功后解锁时，都是卡在了一个骨架屏的加载状态中，而不能立即显示完整的文章内容。只有在刷新之后才能显示完整的文章内容。

## 调试代码已添加位置

### 前端组件
1. **PostContentSmart.tsx** - 核心状态管理组件
   - 组件初始化日志
   - Props同步日志
   - GraphQL查询状态日志
   - 数据更新日志
   - 解锁回调日志
   - 渲染状态日志

2. **PaywallRenderer.tsx** - 付费墙渲染器
   - 组件渲染日志
   - 模式选择日志
   - 条件判断日志
   - 骨架屏显示日志

3. **PaywallCard.tsx** - 付费墙卡片
   - 解锁按钮点击日志
   - 订单创建日志
   - 支付成功处理日志

4. **PaymentProcessor.tsx** - 支付处理器
   - 支付状态检查日志
   - 支付成功确认日志

### 后端组件
1. **cac-graphql.php** - GraphQL解析器
   - Content解析器日志
   - isUnlockedByCurrentUser解析器日志
   - createUnlockOrder mutation日志

2. **cac-db.php** - 数据库操作
   - 解锁状态检查日志
   - 解锁记录写入日志
   - 数据库操作验证日志

3. **cac-core.php** - 核心处理逻辑
   - 支付完成处理日志
   - 解锁记录处理日志

4. **orders.php** - 订单处理
   - 支付完成action触发日志

5. **wallet.php & points.php** - 支付方式
   - 余额/积分支付日志
   - 订单完成触发日志

## 调试标记说明

所有调试日志都使用以下标记前缀，方便过滤和查找：

### 前端标记
- `[DEBUG-PostContentSmart]` - PostContentSmart组件
- `[DEBUG-PaywallRenderer]` - PaywallRenderer组件  
- `[DEBUG-PaywallCard]` - PaywallCard组件
- `[DEBUG-PaymentProcessor]` - PaymentProcessor组件

### 后端标记
- `[DEBUG-GraphQL-Content]` - GraphQL内容解析器
- `[DEBUG-GraphQL-IsUnlocked]` - GraphQL解锁状态解析器
- `[DEBUG-GraphQL-CreateUnlockOrder]` - GraphQL创建解锁订单
- `[DEBUG-DB-Check]` - 数据库解锁状态检查
- `[DEBUG-DB-Write]` - 数据库解锁记录写入
- `[DEBUG-Payment-Handler]` - 支付完成处理器
- `[DEBUG-Payment-Complete]` - 支付完成action
- `[DEBUG-Wallet-Payment]` - 余额支付
- `[DEBUG-Points-Payment]` - 积分支付

## 测试步骤

### 1. 启动日志监控
```bash
./debug-paywall-logs.sh
```

### 2. 执行解锁操作
1. 访问一篇需要付费的文章
2. 点击"立即解锁"按钮
3. 选择支付方式（建议使用余额或积分支付，更容易复现问题）
4. 完成支付
5. 观察是否卡在加载状态

### 3. 观察日志输出
重点关注以下时序：
1. **订单创建**: `[DEBUG-GraphQL-CreateUnlockOrder]`
2. **支付完成**: `[DEBUG-Wallet-Payment]` 或 `[DEBUG-Points-Payment]`
3. **支付完成action**: `[DEBUG-Payment-Complete]`
4. **解锁处理**: `[DEBUG-Payment-Handler]`
5. **数据库写入**: `[DEBUG-DB-Write]`
6. **前端回调**: `[DEBUG-PaywallCard]` 支付成功
7. **内容刷新**: `[DEBUG-PostContentSmart]` refetch
8. **GraphQL查询**: `[DEBUG-GraphQL-Content]` 和 `[DEBUG-GraphQL-IsUnlocked]`
9. **状态更新**: `[DEBUG-PostContentSmart]` 状态变化

### 4. 分析竞态条件
特别注意以下时间点：
- 前端检测到支付成功的时间
- 后端开始处理解锁的时间
- 数据库写入完成的时间
- 前端发起refetch的时间
- GraphQL查询到达后端的时间

## 预期发现的问题

基于之前的分析，预期会发现：
1. 前端支付成功检测过早
2. 后端解锁记录写入延迟
3. GraphQL查询时数据库还未更新
4. PostContentSmart组件卡在loading状态

## 清理调试代码

测试完成后，使用以下命令清理所有调试代码：
```bash
./cleanup-debug-logs.sh
```

## 注意事项

1. 所有调试代码都标记了 `DEBUG-PAYWALL-INVESTIGATION`，方便识别和清理
2. 调试日志会产生大量输出，建议在测试环境使用
3. 清理脚本会删除所有调试代码，请确保已收集足够的调试信息
4. 如需恢复代码，可使用 `git checkout` 命令

## 下一步计划

收集到足够的调试信息后，将根据实际的时序问题制定修复方案，可能包括：
1. 添加适当的延迟机制
2. 实现重试逻辑
3. 优化状态同步机制
4. 改进缓存策略
