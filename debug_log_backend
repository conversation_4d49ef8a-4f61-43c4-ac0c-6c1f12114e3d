  [01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] ========== CREATE UNLOCK ORDER START ==========
[01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] Timestamp: 2025-08-01 23:05:51
[01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] Input: {"callbackUrl":"https:\/\/www.futuredecade.com\/article\/250413-132534\/america-tax","paymentMethod":"wallet","postId":"25168"}
[01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] User ID: 117, Post ID: 25168
[01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] Required level ID: 4
[01-Aug-2025 15:05:51 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25168
[01-Aug-2025 15:05:51 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25168
[01-Aug-2025 15:05:51 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] Already unlocked check: NO
[01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] Unlock price: 0.02
[01-Aug-2025 15:05:51 UTC] [DEBUG] Cache HIT for user_id: 117 member level. Level: {"id":3,"name":"\u5b63\u4ed8\u4f1a\u5458","description":"\u5b63\u5ea6\u4ed8\u8d39","priority":50,"price":0.02,"duration":3,"duration_unit":"months"}
[01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] Creating order with parameters:
[01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] - Title: Unlock post: 《经济学人》| 美国工业将为钢铁关税付出沉重代价
[01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] - Amount: 0.02
[01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] - User ID: 117
[01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] - Payment Method: wallet
[01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] - Metadata: {"order_type":"post_unlock","post_id":25168,"callbackUrl":"https:\/\/www.futuredecade.com\/article\/250413-132534\/america-tax"}
[01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] Order creation result: SUCCESS (ID: 179)
[01-Aug-2025 15:05:51 UTC] [DEBUG-GraphQL-CreateUnlockOrder] ========== CREATE UNLOCK ORDER END ==========
[01-Aug-2025 15:05:55 UTC] [FD WebSocket Push DEBUG] Processing payment success event
[01-Aug-2025 15:05:55 UTC] [FD WebSocket Push DEBUG] Sending post unlock notification for user 117, post 25168
[01-Aug-2025 15:05:55 UTC] [FD WebSocket Push DEBUG] Sending WebSocket event: post:unlocked to target: user_117
[01-Aug-2025 15:05:55 UTC] [FD WebSocket Push DEBUG] WebSocket push sent successfully (non-blocking)
[01-Aug-2025 15:05:55 UTC] [DEBUG-Payment-Complete] Triggering fd_payment_order_completed action for order_id: 179, payment_method: wallet
[01-Aug-2025 15:05:55 UTC] [DEBUG-Payment-Handler] ========== POST UNLOCK PAYMENT HANDLER START ==========
[01-Aug-2025 15:05:55 UTC] [DEBUG-Payment-Handler] Timestamp: 2025-08-01 23:05:55
[01-Aug-2025 15:05:55 UTC] [DEBUG-Payment-Handler] Order ID: 179
[01-Aug-2025 15:05:55 UTC] [DEBUG-Payment-Handler] fd_member_handle_post_unlock_payment triggered for order_id: 179
[01-Aug-2025 15:05:55 UTC] [DEBUG-Payment-Handler] Order found: {"id":"179","user_id":"117","payment_status":"paid","amount":"0.02","product_type":"post_unlock","product_id":"25168"}
[01-Aug-2025 15:05:55 UTC] [DEBUG-Payment-Handler] Raw metadata: {"order_type":"post_unlock","post_id":25168,"callbackUrl":"https:\/\/www.futuredecade.com\/article\/250413-132534\/america-tax"}
[01-Aug-2025 15:05:55 UTC] [DEBUG-Payment-Handler] Parsed metadata: {"order_type":"post_unlock","post_id":25168,"callbackUrl":"https:\/\/www.futuredecade.com\/article\/250413-132534\/america-tax"}
[01-Aug-2025 15:05:55 UTC] [DEBUG-Payment-Handler] Processing unlock for post_id: 25168, user_id: 117, order_id: 179
[01-Aug-2025 15:05:55 UTC] [DEBUG-Payment-Handler] Calling fd_member_record_post_unlock...
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Write] fd_member_record_post_unlock called for user_id: 117, post_id: 25168 at 2025-08-01 23:05:55
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25168
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25168
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] Query result: 0, Status: LOCKED
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Write] Inserting unlock record into fd_unlocked_posts
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Write] Successfully inserted unlock record with ID: 1 for user_id: 117, post_id: 25168
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25168
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25168
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] Query result: 1, Status: UNLOCKED
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Write] Verification check after insert: CONFIRMED
[01-Aug-2025 15:05:55 UTC] [DEBUG-Payment-Handler] fd_member_record_post_unlock result for user 117, post 25168: SUCCESS
[01-Aug-2025 15:05:55 UTC] [DEBUG-Payment-Handler] Post unlock completed successfully!
[01-Aug-2025 15:05:55 UTC] [DEBUG-Payment-Handler] ========== POST UNLOCK PAYMENT HANDLER END ==========
[01-Aug-2025 15:05:55 UTC] [DEBUG-Payment-Complete] fd_payment_order_completed action completed for order_id: 179
[01-Aug-2025 15:05:55 UTC] [DEBUG] Checking access for post_id: 25168, user_id: 117
[01-Aug-2025 15:05:55 UTC] [DEBUG] Post 25168 requires level_id: 4
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25168
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25168
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] Query result: 1, Status: UNLOCKED
[01-Aug-2025 15:05:55 UTC] [DEBUG] Access granted for post_id: 25168, user_id: 117 due to individual unlock.
[01-Aug-2025 15:05:55 UTC] [DEBUG-GraphQL-IsUnlocked] Checking unlock status for post_id: 25168, user_id: 117
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25168
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25168
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] Query result: 1, Status: UNLOCKED
[01-Aug-2025 15:05:55 UTC] [DEBUG-GraphQL-IsUnlocked] Unlock status for post_id: 25168, user_id: 117: UNLOCKED
[01-Aug-2025 15:05:55 UTC] [DEBUG] Checking access for post_id: 25168, user_id: 117
[01-Aug-2025 15:05:55 UTC] [DEBUG] Post 25168 requires level_id: 4
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] fd_member_has_user_unlocked_post called for user_id: 117, post_id: 25168
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] Query: SELECT COUNT(*) FROM fd_unlocked_posts WHERE user_id = 117 AND post_id = 25168
[01-Aug-2025 15:05:55 UTC] [DEBUG-DB-Check] Query result: 1, Status: UNLOCKED
[01-Aug-2025 15:05:55 UTC] [DEBUG] Access granted for post_id: 25168, user_id: 117 due to individual unlock.
                    
感谢使用 WordPress 进行创作。6.8.2 版本
